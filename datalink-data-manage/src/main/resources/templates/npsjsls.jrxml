<?xml version="1.0" encoding="UTF-8"?>
<!-- Created with Jaspersoft Studio version 6.21.3.final using JasperReports Library version 6.21.3-4a3078d20785ebe464f18037d738d12fc98c13cf  -->
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="纳品书受领书" pageWidth="842" pageHeight="595" orientation="Landscape" columnWidth="802" leftMargin="20" rightMargin="20" topMargin="20" bottomMargin="20" uuid="98c10f30-ede5-4f7e-8379-ae5c93c9fb46">
	<property name="com.jaspersoft.studio.data.defaultdataadapter" value="One Empty Record"/>
	<parameter name="asnno" class="java.lang.String"/>
	<parameter name="sapasnNo" class="java.lang.String"/>
	<parameter name="supplierName" class="java.lang.String"/>
	<parameter name="deliveryDate" class="java.lang.String"/>
	<parameter name="companyName" class="java.lang.String"/>
	<parameter name="companyAddress" class="java.lang.String"/>
	<parameter name="c_Phone" class="java.lang.String"/>
	<parameter name="c_FAX" class="java.lang.String"/>
	<parameter name="phone" class="java.lang.String"/>
	<parameter name="fax" class="java.lang.String"/>
	<parameter name="factoryCode" class="java.lang.String"/>
	<parameter name="deliveryAddress" class="java.lang.String"/>
	<parameter name="userLoginName" class="java.lang.String"/>
	<parameter name="asnList" class="java.util.List"/>
	<queryString>
		<![CDATA[]]>
	</queryString>
	<field name="lineNo" class="java.lang.String"/>
	<field name="partNo" class="java.lang.String"/>
	<field name="partDescription" class="java.lang.String"/>
	<field name="orderNo" class="java.lang.String"/>
	<field name="orderLineNo" class="java.lang.String"/>
	<field name="releaseNo" class="java.lang.String"/>
	<field name="shipQty" class="java.math.BigDecimal"/>
	<field name="remark" class="java.lang.String"/>
	<background>
		<band splitType="Stretch"/>
	</background>
	<pageHeader>
		<band height="160" splitType="Stretch">
			<property name="com.jaspersoft.studio.unit.height" value="px"/>
			<staticText>
				<reportElement x="120" y="0" width="159" height="25" uuid="b125e681-5468-4de6-98b5-294cb3c6eee1"/>
				<textElement textAlignment="Center">
					<font fontName="微软雅黑" size="18" isBold="true"/>
				</textElement>
				<text><![CDATA[纳品书]]></text>
			</staticText>
			<staticText>
				<reportElement x="530" y="0" width="138" height="25" uuid="c125e681-5468-4de6-98b5-294cb3c6eee2"/>
				<textElement textAlignment="Center">
					<font fontName="微软雅黑" size="18" isBold="true"/>
				</textElement>
				<text><![CDATA[受领书]]></text>
			</staticText>
			<line>
				<reportElement x="401" y="0" width="1" height="160" uuid="d125e681-5468-4de6-98b5-294cb3c6eee3">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<graphicElement>
					<pen lineWidth="1.0" lineStyle="Dashed"/>
				</graphicElement>
			</line>
			<staticText>
				<reportElement x="188" y="50" width="60" height="15" uuid="e125e681-5468-4de6-98b5-294cb3c6eee4"/>
				<textElement textAlignment="Right">
					<font fontName="微软雅黑" size="10"/>
				</textElement>
				<text><![CDATA[供应商:]]></text>
			</staticText>
			<textField isBlankWhenNull="true">
				<reportElement x="248" y="50" width="150" height="15" uuid="f125e681-5468-4de6-98b5-294cb3c6eee5"/>
				<textElement>
					<font fontName="微软雅黑" size="10"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{supplierName}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="218" y="110" width="80" height="15" uuid="a225e681-5468-4de6-98b5-294cb3c6eee6"/>
				<textElement textAlignment="Right">
					<font fontName="微软雅黑" size="10"/>
				</textElement>
				<text><![CDATA[交货日期:]]></text>
			</staticText>
			<textField isBlankWhenNull="true">
				<reportElement x="298" y="110" width="100" height="15" uuid="b225e681-5468-4de6-98b5-294cb3c6eee7"/>
				<textElement>
					<font fontName="微软雅黑" size="10" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{deliveryDate}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="0" y="110" width="80" height="15" uuid="c225e681-5468-4de6-98b5-294cb3c6eee8"/>
				<textElement>
					<font fontName="微软雅黑" size="10"/>
				</textElement>
				<text><![CDATA[SAP交货单号:]]></text>
			</staticText>
			<textField isBlankWhenNull="true">
				<reportElement x="80" y="110" width="120" height="15" uuid="d225e681-5468-4de6-98b5-294cb3c6eee9"/>
				<textElement>
					<font fontName="微软雅黑" size="10" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{sapasnNo}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="278" y="80" width="120" height="15" uuid="f225e681-5468-4de6-98b5-294cb3c6eef1"/>
				<textElement>
					<font fontName="微软雅黑" size="10"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{asnno}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="218" y="65" width="80" height="15" uuid="a325e681-5468-4de6-98b5-294cb3c6eef2"/>
				<textElement textAlignment="Right">
					<font fontName="微软雅黑" size="10"/>
				</textElement>
				<text><![CDATA[Printed By:]]></text>
			</staticText>
			<textField isBlankWhenNull="true">
				<reportElement x="298" y="65" width="100" height="15" uuid="b325e681-5468-4de6-98b5-294cb3c6eef3"/>
				<textElement>
					<font fontName="微软雅黑" size="10"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{userLoginName}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="404" y="50" width="60" height="15" uuid="c325e681-5468-4de6-98b5-294cb3c6eef4"/>
				<textElement>
					<font fontName="微软雅黑" size="10"/>
				</textElement>
				<text><![CDATA[供应商:]]></text>
			</staticText>
			<textField isBlankWhenNull="true">
				<reportElement x="464" y="50" width="150" height="15" uuid="d325e681-5468-4de6-98b5-294cb3c6eef5"/>
				<textElement>
					<font fontName="微软雅黑" size="10"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{supplierName}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="622" y="110" width="80" height="15" uuid="e325e681-5468-4de6-98b5-294cb3c6eef6"/>
				<textElement textAlignment="Right">
					<font fontName="微软雅黑" size="10"/>
				</textElement>
				<text><![CDATA[交货日期:]]></text>
			</staticText>
			<textField isBlankWhenNull="true">
				<reportElement x="702" y="110" width="100" height="15" uuid="f325e681-5468-4de6-98b5-294cb3c6eef7"/>
				<textElement>
					<font fontName="微软雅黑" size="10" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{deliveryDate}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="404" y="110" width="80" height="15" uuid="a425e681-5468-4de6-98b5-294cb3c6eef8"/>
				<textElement>
					<font fontName="微软雅黑" size="10"/>
				</textElement>
				<text><![CDATA[SAP交货单号:]]></text>
			</staticText>
			<textField isBlankWhenNull="true">
				<reportElement x="484" y="110" width="120" height="15" uuid="b425e681-5468-4de6-98b5-294cb3c6eef9"/>
				<textElement>
					<font fontName="微软雅黑" size="10" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{sapasnNo}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="404" y="65" width="80" height="15" uuid="c425e681-5468-4de6-98b5-294cb3c6eff0"/>
				<textElement>
					<font fontName="微软雅黑" size="10"/>
				</textElement>
				<text><![CDATA[Printed By:]]></text>
			</staticText>
			<textField isBlankWhenNull="true">
				<reportElement x="484" y="65" width="100" height="15" uuid="d425e681-5468-4de6-98b5-294cb3c6eff1"/>
				<textElement>
					<font fontName="微软雅黑" size="10"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{userLoginName}]]></textFieldExpression>
			</textField>
			<image>
				<reportElement x="0" y="0" width="150" height="50" uuid="c29b22ce-b8d9-4a47-b8a3-8a17caf3cc4d"/>
				<imageExpression><![CDATA[javax.imageio.ImageIO.read(
			            new java.io.ByteArrayInputStream(
			                java.util.Base64.getDecoder().decode("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")
			            )
			        )]]></imageExpression>
			</image>
			<textField isBlankWhenNull="true">
				<reportElement x="404" y="80" width="120" height="15" uuid="1612c16c-f24c-4233-933c-0624250230d3"/>
				<textElement>
					<font fontName="微软雅黑" size="10"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{asnno}]]></textFieldExpression>
			</textField>
			<image>
				<reportElement x="404" y="0" width="150" height="50" uuid="cb5ecb03-5eae-4f04-8706-ad9a225b6efd"/>
				<imageExpression><![CDATA[javax.imageio.ImageIO.read(
			            new java.io.ByteArrayInputStream(
			                java.util.Base64.getDecoder().decode("iVBORw0KGgoAAAANSUhEUgAAAJIAAAA4CAYAAAAW/KEGAAAYnUlEQVR4Xu2dCXxV9bXvv+u/9zkJSRhFRWRURiMgVm3tKGpr1ba3g6gVwaFXrQM+7eDt8Nqmr6/e663vqTj0obW2gkXh3dp7nVqrltbXwQEFMSEIiqCCCoIEMp2z93+9zzrZBw7hBEISIVH++UTiyX//h/X/7bV+a9g7wvugzZ86NZi8dVBYPvSAIIp69QvDIFNSvyUzICNNsqAqsze2qFVVroYjwj59CEqzW8JU79IwzEi6XrU8DLTUxZIG9+7azcHayqoz98qa9sa+83PI3pysq+Za/vlvDkylSstTQepgCaR3pLophK1edOuwe3+8tqvmsXG0an56TUm2fGCF6xV5X46k+vs4GiyBO0S9Dk6HqYNKSkp7NzXVD4jiONUyt6zH6RqNeU3Fr0qlwpcr4j6vyJWnNXfl2rrTWN0eSDq1Kv3Kli2TnKQOk9AFqj52odugEjSERC8Nuf9f3+mMQF//7i0HhBIcEqaDQerlQBe6g3FyqARuUK+SdH+PDIwdA8W5gelU2BfVrCKrA+deacxkXsfrCo+u8hq/rgGrB2xcuVaqqnxn1tQTr+12QFoy8VvlZX3jYwSOC8NghHc0EfKUenm+XHTtoEevr99TQa+Y9qM+/fr1GxW5+DCVYFgQuNEEMiaVCod7pLcEUoaTsiAMHSLg7NuBY30Qpmqdc8szLn4xUFkUxrK6KVtX179p3ZYPImDakv0+BVIVuMvHXlj+bq+yDznHp3DBsQSS9QFPBs49/masL3307zc07g44CrLy1Jlp3++A3hXOjY0COTII3HgJZJy4YKwEciDOpUQkJaETA4sEDjGwiMQEkiGQdSqyJAiCJepkSRi6F7aUVKxbWVeTnVJVFe1uDR/03+91INUOvLA3pW6kpFKfK0m7j0UhI13A0xqED4RR5onhS3++aVeHYqBZc/p3+kUqB4ZpOcqJO6YknRoXiVaqkxE459TwEZhGkRxYxAlBGBpoVALZrM6t906f8yJPByIvkvJLDr72qrc+6GDozP73CpBqOH90UOqOD8vCTwVp+XCYDuoanH9SQvd4poS/VVbftrWtTbx9wmUVdZKeJBKOcSFHlYapMZGTSnFuqAHEi6JBiznKg8cFAWEY4KExTIU1XnR5JLoYcbVhSVh90A1XrxLQzghu/7U7SuA9A9JSzjs8jZzicOcESGVQEr6eKfUPELi5ozZuXCksKOoCv115WUVdSfo4SfnjJHAnhmE4LnY6KAyClA9AbcU54LRomjyAghZT1RCG4aLY6TMx+ieXkqXNcfTWyF9VNe0/+PdWAl0GJDM5NZx/cIj/ssJXQSYLNILMAz83jXthJL/a6UAfHjWzZGyQmaAuOJFQPuOCYLKG0sc5CQ04YqDZpnF20DwqzjW5wC1G5M8+lCcaouxz4zaVbJaF+znNewubnUfvNJCqmVqhlE5J4y4Q5JQYVUX/KnBHOY0PDWXBDmRZqXI15a8elKoIp4Sp8NQ4xUmkZHAOLIEgYR4sBQAKHaZxcK4Bp29I4B6IHX9xIn8Z/tC/7ZJT7W2BflDn6zCQljPjUIULA+Rch4yJ0XUef19M9ItK5lW3FuiLTB8WEE4pLQnPinvxcdLS21v4zoCTSgBkYDIPPMd3hDAI0FDeVaeLccH9GrJw5OPXL93Pb7ofXPcYSNWcc1xA8C2HO9UhFQpPNRPfGJJ6aBy/3FK4xWqmDnKUT3VwriATBOkVm/NUKlAikE4AlHItmsjAEwb28+vOyR8i4b449otHP3/z+u4nus6vaC2fL1MOmgzuaIGmwdxxR+dH3TcjtBtILzF9fATXOuSztMDgmSz6wzVsWHgaj2wL/StTg1rKThLiixzuJEX6t4R5JfeVi+HkgVTSAiRJiRK6LS50D6qTuf0z8tcDVt5ct29E8t7MahxyHRcf4Ik/LLjjFP2kOSEKfUPcE5vZetZo7umxe94tkFZwwYGe2GL+XwuQVIy+6PE/WMDcB6swD7ulVfO1AUJ2egCXenRMDjHbmv2YAMk+Swuu1BkcmyV0z8YhdzYGzfdPfvVX7743x7jvRl3HRZWCfMOjHwbGKJoLaLXcWrn/PhQSnn0QbYdA9t3q2z9zm0AyUryCl7/okVmCHAq8A/rDiMZfVBa47tWcP8gRX+qQrwMHtZ1kagFTiEOdbNRSvZ9Qbh9bd+fT7V9uz+u5ivNLS0j9AphWGLhKBL/Q03zGEO7uVL6wO0ilKJBqubA3RLc6ZJpH7fa5M0C/P4o5b+cXvZyvDvSEVQHuXEX77i5L6bAvWR3DdUo8f3z7hZcGPmXRI+AvQEMRwQ0HKgFb37PAEGAisBn4a5H+RyR9bMzCZiZ6YTKXzVkCPEEujLFTGwpMAIy/PWM3EXAMYFr1H8BxQH+7e8YycPFCvvzfadHWOU0k8F8/4W+X3Eb10VZkkMxhc/fIthOQajl7sJBeoPBRh2yMia8Yx6j7hJaMttn6WmbMcMhPQIfuDkCJCjcX/boM0e0T+c2euusHAsuBvmYagJeLSPoq4AbgUeAU4ELgTmAxkD8ou+wjwP9KQFaGpWW3NwPLRcBvgAOAlxIgHAasKjKn9b0deAT4HPAF4H7gKeCTCZB+C9j6Fx3LgC89zBnfj+AS4PfXsejCG1n0a+DTCYimA//RI1G0zUwnq1/GDDuoBwVGCyyP0TOOYM6L+c3VcM5wR3AbyGd1x0Moun8H6uGBFJkrRnHvax0UUlcB6cvA3UA5YFH1Qs1mSdlrgLuSNQ7sAiDZHAbchxNALhpPv688wZmnXcei393IIlvLyYClh2YkIOygiPb9Zds00lLOOjxN6aMePczB6gh/ciVzV+aXuIwZxwfI/TF6cHuW7RAvMGsxDd86kwVxe65po09XAGkAsCzRDk8CVwPPJSal2LRdBSQb28AyPwGTzXkucFOBJvoaMK8T8ukWl+aAVMsXegv9HzSXFKgDPWkcc4xr5NoyzvtqGrk1gzeb364mMHcday6YwsLOlmAUAsnM1KutFmD84lLg33Zh2v45MUOvJCYnT26NI5mGKjTx5oIXmjbjWmuKzHkeMGsXpq0wlzgFuDfhUTZ+H8Bibhcnn7dLpt25kxjnWcGM6xS+bXxH4ZojuPtn+UUvZ/ppgvxHDKXt3Yii2Yho4kTm1bb3ml30ywOpX0JsW2s3A1JFcjjFONLkxKQZB/l34F+SuQxAFgA8odXc4wGLuec5khH4YhrVOJbxtrY4Uuuk9McS82X7MbP6VeC/ukA+3WIIWc7540AXe7SkxaQ1HlnJglxZx1LOHhqSegakXeYsvyOL0jbQOOxoFnRFRDoPJNOGdqDFyj+MNNt3W0AyAm0HVwX8OPHG/k9iZvLLDpMfbB77OQ+k3c3ZXiDZ8D8FvpcQ+mndAgFdtAip5bwbFL3KdLtDrh/Dr7+93aTNuF3gog4U7qigV41jjqn+zrZCjXQW0Lq435Y3FTDPrS2v7QcJgP5fooFMG5nJym/NOJF5TGbqWgPpK0Droje7zjy17+6BRjI5GIh/CMxJCHZnZdNtrpcaptcIMj5EiNGp47j7/9rqXmPqgEbKl8b4wR1ZrcBWRWeOZ86vOnJ9wTVdQbZHAUuAXol5uzaJMeWnsYCrcS/TRK2BtKfuv8WQPt6GOXxfA2mjIP2DHJD8V8Yzx2IfVDPtiJDwHx7t3VEgBEjk8b/OEv54Anfta/ffzIppEFO+BpqaJAxg2zP+Z/En+11ngbQRMM1XGGIzvnROoo3enxppGTNeSCK0BMiNY/i1ucZYnVFA2XOKju4okOy6JMv2rqLzhPiuJWSe28NwgGkk4ytGbG0tHQ1Imrb5BmARZiPnbaWH8kBaARjB31ONVExcVtBnnprN3XOANHvp4WTCqQgDUdkEfiWa+isb71lLq0euZBnTvyvItWb0g1wku/lD47k352K/xHmfVHTBrnNou4ZZPl1rvTxq3srfArhuDHc/1k6Amndk/Me0xo2A3fGt2/HAaYAdvgX6LEzwJWAdcFurzuY4WAXDMEsfFxnLNJcRd5vTTOH/BopF4y0d8k/JnMZ5xgLnJ0S+dQ16FvifCT+zUICZ2RyF6PbtZ0vKKQu/CO5qcEehFop2TxJF17PxrT9QNSUX3pEVTOujhE96dGIL89SnQE7P58KWcf4IxV+Txp0a44cEuNDyRUUSkEbWDSz21eiQzYLblCV+GfQ5QZZ6tPYtXqvtgthSt5d/t1tglTr6vtqHVGNfVPqAPaKlB6PSD7RXrjjDayOiG/C8itc3GVzzBmee2RL6mK8Bby/7OhL8FPV9cYGi/nbWl1xF1cimnHpfyfSDFDfHw2eSpOKaLPHlE7jnwbxAqpma9pQNdcSDAximuAGa0xIaKdIQoBs98VspZEMT8ZYUZe+2LnTrdsJ9vy7orlWlbK6fQCocg+pk0KMI0sPwvl8OBEGYzjmspl1alEeL/5qv/BEHPm7CuVVE2Xlk9Va+UdliCW5aciRh6e9RfyjWz6L2C5ecs40nWLlDBm/h+5keJgS4jOIfj/A3H8Hrj0nnI9QdPTYj+5aQNRNnmfxbWw1kn5sXZtn354FtwdSkn2Xw7feH5O4Z+FGrWJS5/D8BRhRZoPExS6xuSxUlhN2y/sWaydN42JtJvMgqEixDYOaxWLP0yEk5Jxm+DxTLAvw3wGqZLLhr62w7GmNa481l0xC5CufGI5TmelvGs6MtBypfQ8TpXD2+JatwS82nEfcHVAXnlDh76k6Ecz5Tg0mEoyPCjzhkBKiV064KiO4by7wNHV1PJ64rDEhahv7EpEwjP6QdgPEPaxYcNK5U2D4B/Dkh13ZQdrhG3vPNotjmshuvKtasFOXzgOXorFmsyjL2bTWLjNv41s9AYvGpM9ro/HPA6riWAscCxV4yYakVi59ZCY3xq92jomp+mgMnnojXixAxeZjl6MQRWJW8PsTM8RY7g/nzAzYe/UeirK3HfvfwbiskOzF7V11aCCQb0+7wjwJGYK2uyMo2zAuzZpn201tNfE/ieuc/LkyT2GeFQLK6IisFMbmYl2eHbJrMQgUWwDS+kAeSaRFLvrauabIiNqtJ2ndAKhTArOrJhKl/Rf0p201ZB44mDFfx+POjWZBwpptrf4bot5KR3umJQLJb63LADuyPSdFbXjKtgWTBVNMOFsm2MIeBwSLjpjXy9dGFQLLcmyVS881KTyxzb3EgK1Szkpo8kKyy00xOsZZP1+wbjdR6RbOfTZHtXYVwDerzqaA9Q5MEf+fyMXYDt7Rbll8P+s2WCjUe62lAMhNkNT7mjht3MRfdkqqWzTdwtAZS3uxZzs9UvFUgGqcysBgQW2sk+8wK1vLti4mbbibHQFMIJONBFmoo1Eg2vjko3QtItpuqKsfAs38BesGeIShHwhXiS7m8cnbuWvMAB73yCFHmMzmC7nVaTwOS5QEt/WDxmxyNNOcgiUpbhWIhkIxkWw3SyKRa0kpJ/jOpZDQTZneXcaZCjbQIeKAAYHaNxZ1MmxmHKjRtxc7DSPU3uyWQbLW3vXQy3v8etLU53g225Bl88AmuHN3C4W56eTRhthrVFM69yhad2NOAZFF3A4OV0Fqk2Ei0mQ/jNUaIC4FkBNeIqu3ReJFpEwOP1S4Zvzo1qcfeHdk2B8NA+vdE2nnTZonc+1ppJAuy/q7bAummmrMJxCoh9uDcZT3ox7li/HYH5ZZlVoh3Ni6I8dG5XHHEvXsw4B4rxK66oJBsG5Asum2pBtNOBgx7qtfqegqBZHecHbh5eG01KyMxUBUCyVxs847s4YG892emoDDx3PM4Uk4bre6Pb7C1W0S+fU1kNRqfxRWV5tC0tFk1MwjcL1tuILmD9Wsvs+h2TwWSmS1Lc5h2stYaSMaj7OkPS6tYArXwwUMDphFn41YWDzK+lXf/rZjfivPNC3w86WcxJBO+hQGs5YFkd2jea8mL2QBsWslSQXmvzeY3jVjYzKuzzy19Y56hPVxgN0mha29a8/eJVt0z9781TG6trkDdb0E+3b4wQA4WS9Hmf2LmpO0PPtz84ikEqQV43xtxj7NVv8S/jMs9Xd1TgdRaVK2BZEFJO+Q3kkRvYXzGApemeSwJbPzKCGQeSIVke5LlBRNybn3swAuBlOdohWsxeRrpt5xfHkjF+tm4Vpl5SzJusT4GcFurmaKOAkm4pWYy4u5Ata042Y6yVCKE2fjwe1w5evsNOGvZ5wncXag/AOUx6uvP4DvH5G+uHgEkK9z/E2ARbkuo2mNGrZt9ZsEx85oMHEaYLVJtwLCoduvDNlJshN0CgXZIlkC1mJS5+t8p6GwVlVabbaTcErIWWZ+bmFQ7/NYluKaRLFhpd7H1s2RysX7G8axi838k/xbrY4doQcqbEzNr8bOz2xWQtA3cunIoxNegejFoseR0K6nk0h2L8f5qZo7f/nzdrBUlSHwNwg8QCVCdR6rPxVwyeIfnC3uCRiqCm/0ftSmBW2qG49w/I+Hl+Lj/btMj9h5N5A3Uz6KsZBYXjNz+Dqsblo4nXXIj+M8grhn0WgaO+Slnyk417PuB9H7A5MXPpphcfiISXI7KSaBleJ/QoTZSI864siwjjm6kXubluU5OHEbM44ZrCMIrUV9mNf1o/PUdSHcRNf9+EOUHcQ/CrCWHUlJ6FsiFaO4p5BCf0K2CxP6OBFtinDyFxrNoyD7Itydtf920VQ1sbT4f4XsIQxE24vl3Nmy9mapjij0qv03u+zVST4KgZdtvfn4gZeWfhfBsfHwymhTn5UtC8rR92//bG/ByPO1NfPw7NP4lMydY4HW7qrqutjfl+mXE/RCRkajWoX4uUfQTrprQrrf97gdSTwDSHSuGIMHxhO6sHHiQvsTJU1I7ASeBiJkur+/mChW93oNE93N55Y6Vmz9feRBRZhpB6jKcG4WP1+PjeWSzN3H1RHuYtN1tP5DaLaq92HF+dbp/Q8mx9SInZyT4HOorwfUijpLEUFKIli9Iy+sWsdf++rdRWUjk59DY+DTfnrTtDTLbdnDr8o+jeinOnY44+7MYT6H+TjLpBVw9skPvqNoPpL2Ij11NNXHuiiGRuE9l1J3YjPt0RuOD65F0fexzr3fdZohyGihBTu4fe/erW4Fzj9KceQjp9RSXDd+5xvyWZQegYoX8XwOZiLOXV7jfko3uQsJF2/JoHZTHfiB1UHCduWzUrBUlh/RPDfApjo+dmxLFekLk/ZgIUhlFMl5pRmlUqNeWxGALkKzAzGdQ2Yjq31B9DBc8wWurX84X4e+wrlzhfsknEDcd9At4K5XUP4P+hlTDI1yyPaDYmf0kK+vsEPuv350EvvCftb19tnyQR4/x4k6IkWMj1Qmx92HkPVmvRNoS9cyqklUrlXQ0O2FrHDc2SrCOOLsE5U+of5qK7POcWVn8b75ZOkTSR5Jy56FiFY1pstlnc+CJGx7lyqO74jH6nba8XyPtDgV7+PsPzX42NeaQA8eAG+c9lV6Co7zK2Fh1dIymslFMrEpsFT5q+QgDkBCLMzBFWXglK66mqbmpthm3ZItELzVsonqXpmf28oE4+SQSnAzBx3BBhkzD30EWkt36D2Ye3aV/w66YSPYDaQ+B0rr71x9ZN0K9nxTHemQs8hGC4Mgo8gNVpCIHlNiA0wIaC/EYcCwzGyuZWIIVXlxNNtNU04zYn72o3hpHG1eeW5Djamt9v7QUiFh9kT2/N4pU6Ytkmv5ERv5IWfD6DhHqTu6xPZfvB1I7pHTx7GdTfUYMSceaGuFS2Ykay6RY/SSvTIxhgI817REX06JpvIHGq4+FbByrWa43Yq+1kUht5H1NrFQHFRUr1mx9s6n6zCOyYBWIu2gWP5rzQhlaMRnFivo/ikgdsT6dM3dbSmr4xpCm3Y7Tjr12tMt+IBVIbup8DY4+ZHOfyNMvonm0Vz0mDHqNi3w0Lo7jcfZHAk2r5L6Nz8Sx4abeq9THKpujKLMqVpZ71ZfVudWRulfWvVP78sILpuzxH9U56q5V/epSfqhXd0yT16Ob8NR5XeZFnqL58Be4RHIcvLu0DySQZj+rqXWNbw+V2A0HPwRxh4dheLhLlwzFxyMRGaLeE0XRJvW6QXEbcMGGxqbGTbGP13nkjRje9vB2FPs3695pWHdPe8zRLk79hLmvD2lKxcN95CuyngGZAOJYMg2B1K556a5lrZ+17y4Ayq/jfQ2kHz38Tp90r/iwSPVwcQxXkWGoDkOkn6hqWFLalO7V652m+oYtPpt9Cxev1UjXSWmw1se6mUgatFfQII0DGqqmSGdfYbj97OfPDz5X+rFDfaSlXn3fKNK+TalgvWuWjfV1TXWLLjl8W51PdwNMW+t5XwKp6oG1ZVGf8NDQ22NIrgmyTc0B9SVxaRayEdRHa3uPiGd/iEhyT0i8923q/Nd6QXka3knHQb9wY93WTb0OjvSRp+/Jdndt0x7p/H+K03Q+i+ZzwwAAAABJRU5ErkJggg==")
			            )
			        )]]></imageExpression>
			</image>
			<textField>
				<reportElement x="310" y="0" width="88" height="20" uuid="f0e1e1a3-b2bb-4064-94bf-77a7b822ddcb"/>
				<textElement textAlignment="Right"/>
				<textFieldExpression><![CDATA[$V{PAGE_NUMBER} +" / " + $V{PAGE_NUMBER}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="714" y="0" width="88" height="20" uuid="47b2d47e-0893-4328-9a79-76c65e9f4aed"/>
				<textElement textAlignment="Right"/>
				<textFieldExpression><![CDATA[$V{PAGE_NUMBER} +" / " + $V{PAGE_NUMBER}]]></textFieldExpression>
			</textField>
			<componentElement>
				<reportElement x="0" y="125" width="165" height="30" uuid="ad0b953e-18be-4491-bded-0aebd54c7fec"/>
				<jr:Code39 xmlns:jr="http://jasperreports.sourceforge.net/jasperreports/components" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports/components http://jasperreports.sourceforge.net/xsd/components.xsd" textPosition="bottom">
					<jr:codeExpression><![CDATA[$P{sapasnNo}]]></jr:codeExpression>
				</jr:Code39>
			</componentElement>
			<componentElement>
				<reportElement x="233" y="125" width="165" height="30" uuid="f990744e-fa52-4aaf-b63e-7add762d12e1"/>
				<jr:Code39 xmlns:jr="http://jasperreports.sourceforge.net/jasperreports/components" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports/components http://jasperreports.sourceforge.net/xsd/components.xsd" textPosition="bottom">
					<jr:codeExpression><![CDATA[$P{asnno}]]></jr:codeExpression>
				</jr:Code39>
			</componentElement>
			<componentElement>
				<reportElement x="637" y="125" width="165" height="30" uuid="9817ebd8-9c09-4ea3-bdd2-17f295230734"/>
				<jr:Code39 xmlns:jr="http://jasperreports.sourceforge.net/jasperreports/components" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports/components http://jasperreports.sourceforge.net/xsd/components.xsd" textPosition="bottom">
					<jr:codeExpression><![CDATA[$P{asnno}]]></jr:codeExpression>
				</jr:Code39>
			</componentElement>
			<componentElement>
				<reportElement x="404" y="125" width="165" height="30" uuid="4e28a534-a425-4091-b313-022e0960fece"/>
				<jr:Code39 xmlns:jr="http://jasperreports.sourceforge.net/jasperreports/components" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports/components http://jasperreports.sourceforge.net/xsd/components.xsd" textPosition="bottom">
					<jr:codeExpression><![CDATA[$P{sapasnNo}]]></jr:codeExpression>
				</jr:Code39>
			</componentElement>
		</band>
	</pageHeader>
	<columnHeader>
		<band height="32" splitType="Stretch">
			<property name="com.jaspersoft.studio.unit.height" value="px"/>
			<staticText>
				<reportElement x="0" y="0" width="30" height="32" uuid="e425e681-5468-4de6-98b5-294cb3c6eff2">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<box>
					<pen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="微软雅黑" size="8"/>
				</textElement>
				<text><![CDATA[NO]]></text>
			</staticText>
			<staticText>
				<reportElement x="30" y="0" width="85" height="32" uuid="f425e681-5468-4de6-98b5-294cb3c6eff3">
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<box>
					<pen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="微软雅黑" size="8"/>
				</textElement>
				<text><![CDATA[客户部品番号
（零件号）
PART NO]]></text>
			</staticText>
			<staticText>
				<reportElement x="115" y="0" width="145" height="32" uuid="a525e681-5468-4de6-98b5-294cb3c6eff4">
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<box>
					<pen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="微软雅黑" size="8"/>
				</textElement>
				<text><![CDATA[订单号-明细-连番
部件名称
PART NAME]]></text>
			</staticText>
			<staticText>
				<reportElement x="260" y="0" width="50" height="32" uuid="b525e681-5468-4de6-98b5-294cb3c6eff5"/>
				<box>
					<pen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="微软雅黑" size="8"/>
				</textElement>
				<text><![CDATA[发注
数量
PQ QTY]]></text>
			</staticText>
			<staticText>
				<reportElement x="310" y="0" width="40" height="32" uuid="c525e681-5468-4de6-98b5-294cb3c6eff6"/>
				<box>
					<pen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="微软雅黑" size="8"/>
				</textElement>
				<text><![CDATA[完纳
ENEND]]></text>
			</staticText>
			<staticText>
				<reportElement x="350" y="0" width="48" height="32" uuid="d525e681-5468-4de6-98b5-294cb3c6eff7">
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<box>
					<pen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="微软雅黑" size="8"/>
				</textElement>
				<text><![CDATA[备注
NOTE]]></text>
			</staticText>
			<staticText>
				<reportElement x="404" y="0" width="30" height="32" uuid="e525e681-5468-4de6-98b5-294cb3c6eff8"/>
				<box>
					<pen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="微软雅黑" size="8"/>
				</textElement>
				<text><![CDATA[NO]]></text>
			</staticText>
			<staticText>
				<reportElement x="434" y="0" width="85" height="32" uuid="f525e681-5468-4de6-98b5-294cb3c6eff9">
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<box>
					<pen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="微软雅黑" size="8"/>
				</textElement>
				<text><![CDATA[客户部品番号
（零件号）
PART NO]]></text>
			</staticText>
			<staticText>
				<reportElement x="519" y="0" width="145" height="32" uuid="a625e681-5468-4de6-98b5-294cb3c6f000">
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<box>
					<pen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="微软雅黑" size="8"/>
				</textElement>
				<text><![CDATA[订单号-明细-连番
部件名称
PART NAME]]></text>
			</staticText>
			<staticText>
				<reportElement x="664" y="0" width="50" height="32" uuid="b625e681-5468-4de6-98b5-294cb3c6f001"/>
				<box>
					<pen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="微软雅黑" size="8"/>
				</textElement>
				<text><![CDATA[发注
数量
PQ QTY]]></text>
			</staticText>
			<staticText>
				<reportElement x="714" y="0" width="40" height="32" uuid="c625e681-5468-4de6-98b5-294cb3c6f002"/>
				<box>
					<pen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="微软雅黑" size="8"/>
				</textElement>
				<text><![CDATA[完纳
ENEND]]></text>
			</staticText>
			<staticText>
				<reportElement x="754" y="0" width="48" height="32" uuid="d625e681-5468-4de6-98b5-294cb3c6f003">
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<box>
					<pen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="微软雅黑" size="8"/>
				</textElement>
				<text><![CDATA[备注
NOTE]]></text>
			</staticText>
			<line>
				<reportElement x="401" y="0" width="1" height="32" uuid="df6ebccf-29e5-48ae-8b4a-f6d499823057">
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<graphicElement>
					<pen lineStyle="Dashed"/>
				</graphicElement>
			</line>
		</band>
	</columnHeader>
	<detail>
		<band height="22" splitType="Stretch">
			<property name="com.jaspersoft.studio.unit.height" value="px"/>
			<textField textAdjust="ScaleFont" isBlankWhenNull="true">
				<reportElement x="0" y="0" width="30" height="22" uuid="e625e681-5468-4de6-98b5-294cb3c6f004">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<box>
					<pen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="微软雅黑" size="9"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{lineNo}]]></textFieldExpression>
			</textField>
			<textField textAdjust="ScaleFont" isBlankWhenNull="true">
				<reportElement x="30" y="0" width="85" height="22" uuid="f625e681-5468-4de6-98b5-294cb3c6f005">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<box>
					<pen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="微软雅黑" size="9"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{partNo}]]></textFieldExpression>
			</textField>
			<textField textAdjust="ScaleFont" isBlankWhenNull="true">
				<reportElement x="115" y="0" width="145" height="22" uuid="a725e681-5468-4de6-98b5-294cb3c6f006">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<box>
					<pen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="微软雅黑" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{orderNo} + "-" + ($F{orderLineNo} != null ? $F{orderLineNo} : "") + "-" + ($F{releaseNo} != null ? $F{releaseNo} : "") + "\n" + ($F{partDescription} != null ? $F{partDescription} : "")]]></textFieldExpression>
			</textField>
			<textField textAdjust="ScaleFont" isBlankWhenNull="true">
				<reportElement x="260" y="0" width="50" height="22" uuid="b725e681-5468-4de6-98b5-294cb3c6f007">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<box>
					<pen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="微软雅黑" size="9"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{shipQty}]]></textFieldExpression>
			</textField>
			<textField textAdjust="ScaleFont" isBlankWhenNull="true">
				<reportElement x="310" y="0" width="40" height="22" uuid="c725e681-5468-4de6-98b5-294cb3c6f008">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<box>
					<pen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="微软雅黑" size="9"/>
				</textElement>
				<textFieldExpression><![CDATA[""]]></textFieldExpression>
			</textField>
			<textField textAdjust="ScaleFont" isBlankWhenNull="true">
				<reportElement x="350" y="0" width="48" height="22" uuid="d725e681-5468-4de6-98b5-294cb3c6f009">
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<box>
					<pen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="微软雅黑" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{remark}]]></textFieldExpression>
			</textField>
			<textField textAdjust="ScaleFont" isBlankWhenNull="true">
				<reportElement x="404" y="0" width="30" height="22" uuid="e725e681-5468-4de6-98b5-294cb3c6f010">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<box>
					<pen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="微软雅黑" size="9"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{lineNo}]]></textFieldExpression>
			</textField>
			<textField textAdjust="ScaleFont" isBlankWhenNull="true">
				<reportElement x="434" y="0" width="85" height="22" uuid="f725e681-5468-4de6-98b5-294cb3c6f011">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<box>
					<pen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="微软雅黑" size="9"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{partNo}]]></textFieldExpression>
			</textField>
			<textField textAdjust="ScaleFont" isBlankWhenNull="true">
				<reportElement x="519" y="0" width="145" height="22" uuid="a825e681-5468-4de6-98b5-294cb3c6f012">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<box>
					<pen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="微软雅黑" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{orderNo} + "-" + ($F{orderLineNo} != null ? $F{orderLineNo} : "") + "-" + ($F{releaseNo} != null ? $F{releaseNo} : "") + "\n" + ($F{partDescription} != null ? $F{partDescription} : "")]]></textFieldExpression>
			</textField>
			<textField textAdjust="ScaleFont" isBlankWhenNull="true">
				<reportElement x="664" y="0" width="50" height="22" uuid="b825e681-5468-4de6-98b5-294cb3c6f013">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<box>
					<pen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="微软雅黑" size="9"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{shipQty}]]></textFieldExpression>
			</textField>
			<textField textAdjust="ScaleFont" isBlankWhenNull="true">
				<reportElement x="714" y="0" width="40" height="22" uuid="c825e681-5468-4de6-98b5-294cb3c6f014">
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<box>
					<pen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="微软雅黑" size="9"/>
				</textElement>
				<textFieldExpression><![CDATA[""]]></textFieldExpression>
			</textField>
			<textField textAdjust="ScaleFont" isBlankWhenNull="true">
				<reportElement x="754" y="0" width="48" height="22" uuid="d825e681-5468-4de6-98b5-294cb3c6f015">
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
					<property name="com.jaspersoft.studio.unit.height" value="px"/>
				</reportElement>
				<box>
					<pen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="微软雅黑" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{remark}]]></textFieldExpression>
			</textField>
			<line>
				<reportElement x="401" y="0" width="1" height="22" uuid="7855e837-7d88-44c6-9294-e1c3103d1165">
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<graphicElement>
					<pen lineStyle="Dashed"/>
				</graphicElement>
			</line>
		</band>
	</detail>
	<pageFooter>
		<band height="70" splitType="Stretch">
			<property name="com.jaspersoft.studio.unit.height" value="px"/>
			<staticText>
				<reportElement x="10" y="10" width="75" height="15" uuid="e825e681-5468-4de6-98b5-294cb3c6f016">
					<property name="com.jaspersoft.studio.unit.width" value="px"/>
				</reportElement>
				<textElement>
					<font fontName="微软雅黑" size="10"/>
				</textElement>
				<text><![CDATA[工厂(库存地点):]]></text>
			</staticText>
			<textField isBlankWhenNull="true">
				<reportElement x="85" y="10" width="200" height="15" uuid="f825e681-5468-4de6-98b5-294cb3c6f017"/>
				<textElement>
					<font fontName="微软雅黑" size="10"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{factoryCode} + "(" + $P{deliveryAddress} + ")"]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="10" y="25" width="60" height="15" uuid="a925e681-5468-4de6-98b5-294cb3c6f018"/>
				<textElement>
					<font fontName="微软雅黑" size="10"/>
				</textElement>
				<text><![CDATA[备注:]]></text>
			</staticText>
			<staticText>
				<reportElement x="298" y="10" width="100" height="25" uuid="b925e681-5468-4de6-98b5-294cb3c6f019"/>
				<box>
					<pen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="微软雅黑" size="10"/>
				</textElement>
				<text><![CDATA[受领印(完结时)]]></text>
			</staticText>
			<staticText>
				<reportElement x="414" y="10" width="75" height="15" uuid="c925e681-5468-4de6-98b5-294cb3c6f020"/>
				<textElement>
					<font fontName="微软雅黑" size="10"/>
				</textElement>
				<text><![CDATA[工厂(库存地点):]]></text>
			</staticText>
			<textField isBlankWhenNull="true">
				<reportElement x="489" y="10" width="200" height="15" uuid="d925e681-5468-4de6-98b5-294cb3c6f021"/>
				<textElement>
					<font fontName="微软雅黑" size="10"/>
				</textElement>
				<textFieldExpression><![CDATA[$P{factoryCode} + "(" + $P{deliveryAddress} + ")"]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="414" y="25" width="60" height="15" uuid="e925e681-5468-4de6-98b5-294cb3c6f022"/>
				<textElement>
					<font fontName="微软雅黑" size="10"/>
				</textElement>
				<text><![CDATA[备注:]]></text>
			</staticText>
			<staticText>
				<reportElement x="702" y="10" width="100" height="25" uuid="f925e681-5468-4de6-98b5-294cb3c6f023"/>
				<box>
					<pen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="微软雅黑" size="10"/>
				</textElement>
				<text><![CDATA[受领印(完结时)]]></text>
			</staticText>
			<staticText>
				<reportElement x="702" y="35" width="100" height="35" uuid="ce9fa428-df72-4d02-9952-85cac57b9d8f"/>
				<box>
					<pen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="微软雅黑" size="10"/>
				</textElement>
				<text><![CDATA[]]></text>
			</staticText>
			<staticText>
				<reportElement x="298" y="35" width="100" height="35" uuid="e3f40466-57ab-4d16-b0db-f58fafed2f61"/>
				<box>
					<pen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font fontName="微软雅黑" size="10"/>
				</textElement>
				<text><![CDATA[]]></text>
			</staticText>
		</band>
	</pageFooter>
</jasperReport>
