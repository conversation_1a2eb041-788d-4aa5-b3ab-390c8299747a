<?xml version="1.0" encoding="UTF-8"?>
<!-- Created with Jaspersoft Studio version 6.21.3.final using JasperReports Library version 6.21.3-4a3078d20785ebe464f18037d738d12fc98c13cf  -->
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="ProdTagReport" pageWidth="842" pageHeight="595" orientation="Landscape" columnWidth="802" leftMargin="20" rightMargin="20" topMargin="20" bottomMargin="20" uuid="c86295d7-a61b-4f63-ae1b-efbdfbd3ff43">
	<property name="com.jaspersoft.studio.data.defaultdataadapter" value="One Empty Record"/>
	<property name="net.sf.jasperreports.export.pdf.dpi" value="300"/>
	<style name="baseStyle" fontName="微软雅黑" fontSize="10">
		<box>
			<pen lineWidth="0.5" lineStyle="Solid"/>
			<topPen lineWidth="0.5" lineStyle="Solid"/>
			<leftPen lineWidth="0.5" lineStyle="Solid"/>
			<bottomPen lineWidth="0.5" lineStyle="Solid"/>
			<rightPen lineWidth="0.5" lineStyle="Solid"/>
		</box>
		<paragraph leftIndent="2" rightIndent="2"/>
	</style>
	<field name="partNo" class="java.lang.String"/>
	<field name="partDescription" class="java.lang.String"/>
	<field name="supplierCode" class="java.lang.String"/>
	<field name="supplierName" class="java.lang.String"/>
	<field name="dueDate" class="java.lang.String"/>
	<field name="snp" class="java.math.BigDecimal"/>
	<field name="shipQty" class="java.math.BigDecimal"/>
	<field name="companyName" class="java.lang.String"/>
	<field name="orderNo" class="java.lang.String"/>
	<field name="orderLineNo" class="java.lang.String"/>
	<field name="releaseNo" class="java.lang.String"/>
	<field name="label" class="java.lang.String"/>
	<field name="batch" class="java.lang.String"/>
	<field name="tailSNP" class="java.lang.Boolean"/>
	<field name="qrCodeData" class="java.lang.String"/>
	<detail>
		<band height="180" splitType="Stretch">
			<property name="com.jaspersoft.studio.unit.height" value="px"/>
			<!-- 现品票表格 -->
			<rectangle>
				<reportElement x="0" y="0" width="400" height="180" uuid="main-border"/>
				<graphicElement>
					<pen lineWidth="1.0"/>
				</graphicElement>
			</rectangle>
			
			<!-- 第一行：部品番号 -->
			<rectangle>
				<reportElement x="0" y="0" width="300" height="30" uuid="part-no-border"/>
				<graphicElement>
					<pen lineWidth="0.5"/>
				</graphicElement>
			</rectangle>
			<staticText>
				<reportElement x="5" y="5" width="60" height="20" uuid="part-no-label"/>
				<textElement verticalAlignment="Middle">
					<font fontName="微软雅黑" size="12" isBold="true"/>
				</textElement>
				<text><![CDATA[部品番号：]]></text>
			</staticText>
			<textField isBlankWhenNull="true">
				<reportElement x="65" y="5" width="230" height="20" uuid="part-no-value"/>
				<textElement verticalAlignment="Middle">
					<font fontName="微软雅黑" size="16" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{partNo}]]></textFieldExpression>
			</textField>
			
			<!-- QR码区域 -->
			<rectangle>
				<reportElement x="300" y="0" width="100" height="60" uuid="qr-border"/>
				<graphicElement>
					<pen lineWidth="0.5"/>
				</graphicElement>
			</rectangle>
			<componentElement>
				<reportElement x="310" y="10" width="80" height="40" uuid="qr-code"/>
				<jr:QRCode xmlns:jr="http://jasperreports.sourceforge.net/jasperreports/components" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports/components http://jasperreports.sourceforge.net/xsd/components.xsd">
					<jr:codeExpression><![CDATA[$F{qrCodeData}]]></jr:codeExpression>
				</jr:QRCode>
			</componentElement>
			
			<!-- 第二行：部品名称和批次 -->
			<rectangle>
				<reportElement x="0" y="30" width="400" height="25" uuid="part-desc-border"/>
				<graphicElement>
					<pen lineWidth="0.5"/>
				</graphicElement>
			</rectangle>
			<staticText>
				<reportElement x="5" y="35" width="60" height="15" uuid="part-desc-label"/>
				<textElement verticalAlignment="Middle">
					<font fontName="微软雅黑" size="10"/>
				</textElement>
				<text><![CDATA[部品名称：]]></text>
			</staticText>
			<textField isBlankWhenNull="true">
				<reportElement x="65" y="35" width="200" height="15" uuid="part-desc-value"/>
				<textElement verticalAlignment="Middle">
					<font fontName="微软雅黑" size="10"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{partDescription}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="270" y="35" width="30" height="15" uuid="batch-label"/>
				<textElement verticalAlignment="Middle">
					<font fontName="微软雅黑" size="10"/>
				</textElement>
				<text><![CDATA[批次:]]></text>
			</staticText>
			<textField isBlankWhenNull="true">
				<reportElement x="300" y="35" width="95" height="15" uuid="batch-value"/>
				<textElement verticalAlignment="Middle">
					<font fontName="微软雅黑" size="10"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{batch}]]></textFieldExpression>
			</textField>
			
			<!-- 第三行：供应商 -->
			<rectangle>
				<reportElement x="0" y="55" width="400" height="25" uuid="supplier-border"/>
				<graphicElement>
					<pen lineWidth="0.5"/>
				</graphicElement>
			</rectangle>
			<staticText>
				<reportElement x="5" y="60" width="40" height="15" uuid="supplier-label"/>
				<textElement verticalAlignment="Middle">
					<font fontName="微软雅黑" size="10"/>
				</textElement>
				<text><![CDATA[供应商]]></text>
			</staticText>
			<textField isBlankWhenNull="true">
				<reportElement x="45" y="60" width="350" height="15" uuid="supplier-value"/>
				<textElement verticalAlignment="Middle">
					<font fontName="微软雅黑" size="10"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{supplierCode} + "  " + $F{supplierName}]]></textFieldExpression>
			</textField>
			
			<!-- 第四行：到货指示时期、SNP、数量 -->
			<rectangle>
				<reportElement x="0" y="80" width="133" height="40" uuid="due-date-border"/>
				<graphicElement>
					<pen lineWidth="0.5"/>
				</graphicElement>
			</rectangle>
			<staticText>
				<reportElement x="5" y="85" width="80" height="15" uuid="due-date-label"/>
				<textElement verticalAlignment="Middle">
					<font fontName="微软雅黑" size="9"/>
				</textElement>
				<text><![CDATA[到货指示时期]]></text>
			</staticText>
			<textField isBlankWhenNull="true">
				<reportElement x="5" y="100" width="123" height="15" uuid="due-date-value"/>
				<textElement verticalAlignment="Middle" textAlignment="Center">
					<font fontName="微软雅黑" size="12" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{dueDate}]]></textFieldExpression>
			</textField>
			
			<rectangle>
				<reportElement x="133" y="80" width="134" height="40" uuid="snp-border"/>
				<graphicElement>
					<pen lineWidth="0.5"/>
				</graphicElement>
			</rectangle>
			<staticText>
				<reportElement x="138" y="85" width="30" height="15" uuid="snp-label"/>
				<textElement verticalAlignment="Middle">
					<font fontName="微软雅黑" size="9"/>
				</textElement>
				<text><![CDATA[SNP]]></text>
			</staticText>
			<textField isBlankWhenNull="true">
				<reportElement x="138" y="100" width="124" height="15" uuid="snp-value"/>
				<textElement verticalAlignment="Middle" textAlignment="Center">
					<font fontName="微软雅黑" size="12" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{snp}]]></textFieldExpression>
			</textField>
			<!-- SNP特殊样式：tailSNP时黑底白字 -->
			<rectangle>
				<reportElement x="138" y="100" width="124" height="15" uuid="snp-bg">
					<printWhenExpression><![CDATA[$F{tailSNP}]]></printWhenExpression>
				</reportElement>
				<graphicElement>
					<pen lineWidth="0.0"/>
				</graphicElement>
			</rectangle>
			<textField isBlankWhenNull="true">
				<reportElement x="138" y="100" width="124" height="15" uuid="snp-value-tail">
					<printWhenExpression><![CDATA[$F{tailSNP}]]></printWhenExpression>
				</reportElement>
				<textElement verticalAlignment="Middle" textAlignment="Center">
					<font fontName="微软雅黑" size="12" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{snp}]]></textFieldExpression>
			</textField>
			
			<rectangle>
				<reportElement x="267" y="80" width="133" height="40" uuid="qty-border"/>
				<graphicElement>
					<pen lineWidth="0.5"/>
				</graphicElement>
			</rectangle>
			<staticText>
				<reportElement x="272" y="85" width="30" height="15" uuid="qty-label"/>
				<textElement verticalAlignment="Middle">
					<font fontName="微软雅黑" size="9"/>
				</textElement>
				<text><![CDATA[数量]]></text>
			</staticText>
			<textField isBlankWhenNull="true">
				<reportElement x="272" y="100" width="123" height="15" uuid="qty-value"/>
				<textElement verticalAlignment="Middle" textAlignment="Center">
					<font fontName="微软雅黑" size="12" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{shipQty}]]></textFieldExpression>
			</textField>
			
			<!-- 第五行：公司名称 -->
			<rectangle>
				<reportElement x="0" y="120" width="400" height="25" uuid="company-border"/>
				<graphicElement>
					<pen lineWidth="0.5"/>
				</graphicElement>
			</rectangle>
			<staticText>
				<reportElement x="5" y="125" width="60" height="15" uuid="company-label"/>
				<textElement verticalAlignment="Middle">
					<font fontName="微软雅黑" size="10"/>
				</textElement>
				<text><![CDATA[公司名称:]]></text>
			</staticText>
			<textField isBlankWhenNull="true">
				<reportElement x="65" y="125" width="330" height="15" uuid="company-value"/>
				<textElement verticalAlignment="Middle">
					<font fontName="微软雅黑" size="10"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{companyName}]]></textFieldExpression>
			</textField>
			
			<!-- 第六行：订单号、行号下达号、系列编号 -->
			<rectangle>
				<reportElement x="0" y="145" width="133" height="35" uuid="order-border"/>
				<graphicElement>
					<pen lineWidth="0.5"/>
				</graphicElement>
			</rectangle>
			<staticText>
				<reportElement x="5" y="150" width="40" height="15" uuid="order-label"/>
				<textElement verticalAlignment="Middle">
					<font fontName="微软雅黑" size="9"/>
				</textElement>
				<text><![CDATA[订单号]]></text>
			</staticText>
			<textField isBlankWhenNull="true">
				<reportElement x="5" y="165" width="123" height="10" uuid="order-value"/>
				<textElement verticalAlignment="Middle">
					<font fontName="微软雅黑" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{orderNo}]]></textFieldExpression>
			</textField>
			
			<rectangle>
				<reportElement x="133" y="145" width="134" height="35" uuid="line-border"/>
				<graphicElement>
					<pen lineWidth="0.5"/>
				</graphicElement>
			</rectangle>
			<staticText>
				<reportElement x="138" y="150" width="60" height="15" uuid="line-label"/>
				<textElement verticalAlignment="Middle">
					<font fontName="微软雅黑" size="9"/>
				</textElement>
				<text><![CDATA[行号 下达号]]></text>
			</staticText>
			<textField isBlankWhenNull="true">
				<reportElement x="138" y="165" width="124" height="10" uuid="line-value"/>
				<textElement verticalAlignment="Middle">
					<font fontName="微软雅黑" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{orderLineNo} + "  " + $F{releaseNo}]]></textFieldExpression>
			</textField>
			
			<rectangle>
				<reportElement x="267" y="145" width="133" height="35" uuid="label-border"/>
				<graphicElement>
					<pen lineWidth="0.5"/>
				</graphicElement>
			</rectangle>
			<staticText>
				<reportElement x="272" y="150" width="60" height="15" uuid="label-label"/>
				<textElement verticalAlignment="Middle">
					<font fontName="微软雅黑" size="9"/>
				</textElement>
				<text><![CDATA[系列编号]]></text>
			</staticText>
			<textField isBlankWhenNull="true">
				<reportElement x="272" y="165" width="123" height="10" uuid="label-value"/>
				<textElement verticalAlignment="Middle">
					<font fontName="微软雅黑" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{label}]]></textFieldExpression>
			</textField>
		</band>
	</detail>
</jasperReport>
