<?xml version="1.0" encoding="UTF-8"?>
<!-- Created with Jaspersoft Studio version 6.21.3.final using JasperReports Library version 6.21.3-4a3078d20785ebe464f18037d738d12fc98c13cf  -->
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="PalletTagReport" columnCount="2" printOrder="Horizontal" pageWidth="842" pageHeight="595" orientation="Landscape" columnWidth="391" columnSpacing="20" leftMargin="20" rightMargin="20" topMargin="20" bottomMargin="20" uuid="a1b2c3d4-e5f6-7890-abcd-ef1234567890">
	<property name="com.jaspersoft.studio.data.defaultdataadapter" value="One Empty Record"/>
	<property name="net.sf.jasperreports.export.pdf.dpi" value="300"/>
	<style name="baseStyle" fontName="微软雅黑" fontSize="8">
		<box>
			<pen lineWidth="0.5" lineStyle="Solid"/>
			<topPen lineWidth="0.5" lineStyle="Solid"/>
			<leftPen lineWidth="0.5" lineStyle="Solid"/>
			<bottomPen lineWidth="0.5" lineStyle="Solid"/>
			<rightPen lineWidth="0.5" lineStyle="Solid"/>
		</box>
		<paragraph leftIndent="2" rightIndent="2"/>
	</style>
	<field name="partNo" class="java.lang.String"/>
	<field name="partDescription" class="java.lang.String"/>
	<field name="supplierCode" class="java.lang.String"/>
	<field name="supplierName" class="java.lang.String"/>
	<field name="dueDate" class="java.lang.String"/>
	<field name="pallet" class="java.lang.Integer"/>
	<field name="snp" class="java.lang.Integer"/>
	<field name="orderNo" class="java.lang.String"/>
	<field name="orderLineNo" class="java.lang.String"/>
	<field name="factoryCode" class="java.lang.String"/>
	<field name="deliveryAddress" class="java.lang.String"/>
	<field name="label" class="java.lang.String"/>
	<field name="tailTag" class="java.lang.Boolean"/>
	<field name="tailSNPQty" class="java.math.BigDecimal"/>
	<detail>
		<band height="180" splitType="Stretch">
			<property name="com.jaspersoft.studio.unit.height" value="px"/>
			<rectangle>
				<reportElement x="0" y="0" width="391" height="170" uuid="b1c2d3e4-f5a6-7b8c-9d0e-1f2a3b4c5d6e"/>
				<graphicElement>
					<pen lineWidth="1.0"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="0" y="0" width="391" height="30" uuid="c2d3e4f5-a6b7-8c9d-0e1f-2a3b4c5d6e7f"/>
				<graphicElement>
					<pen lineWidth="0.5"/>
				</graphicElement>
			</rectangle>
			<staticText>
				<reportElement x="5" y="8" width="60" height="15" uuid="d3e4f5a6-b7c8-9d0e-1f2a-3b4c5d6e7f8a"/>
				<textElement verticalAlignment="Middle">
					<font fontName="微软雅黑" size="10" isBold="true"/>
				</textElement>
				<text><![CDATA[Part No:]]></text>
			</staticText>
			<textField>
				<reportElement x="65" y="8" width="200" height="15" uuid="e4f5a6b7-c8d9-0e1f-2a3b-4c5d6e7f8a9b"/>
				<textElement verticalAlignment="Middle">
					<font fontName="微软雅黑" size="14" isBold="true"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{partNo}]]></textFieldExpression>
			</textField>
			<image>
				<reportElement x="270" y="5" width="115" height="20" uuid="f5a6b7c8-d9e0-1f2a-3b4c-5d6e7f8a9b0c"/>
				<imageExpression><![CDATA[javax.imageio.ImageIO.read(
			            new java.io.ByteArrayInputStream(
			                java.util.Base64.getDecoder().decode("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")
			            )
			        )]]></imageExpression>
			</image>
			<rectangle>
				<reportElement x="0" y="30" width="391" height="20" uuid="a6b7c8d9-e0f1-2a3b-4c5d-6e7f8a9b0c1d"/>
				<graphicElement>
					<pen lineWidth="0.5"/>
				</graphicElement>
			</rectangle>
			<staticText>
				<reportElement x="5" y="33" width="60" height="15" uuid="b7c8d9e0-f1a2-3b4c-5d6e-7f8a9b0c1d2e"/>
				<textElement verticalAlignment="Middle">
					<font fontName="微软雅黑" size="8"/>
				</textElement>
				<text><![CDATA[PartName:]]></text>
			</staticText>
			<textField>
				<reportElement x="65" y="33" width="320" height="15" uuid="c8d9e0f1-a2b3-4c5d-6e7f-8a9b0c1d2e3f"/>
				<textElement verticalAlignment="Middle">
					<font fontName="微软雅黑" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{partDescription}]]></textFieldExpression>
			</textField>
			<rectangle>
				<reportElement x="0" y="50" width="391" height="20" uuid="d9e0f1a2-b3c4-5d6e-7f8a-9b0c1d2e3f4a"/>
				<graphicElement>
					<pen lineWidth="0.5"/>
				</graphicElement>
			</rectangle>
			<staticText>
				<reportElement x="5" y="53" width="50" height="15" uuid="e0f1a2b3-c4d5-6e7f-8a9b-0c1d2e3f4a5b"/>
				<textElement verticalAlignment="Middle">
					<font fontName="微软雅黑" size="8"/>
				</textElement>
				<text><![CDATA[Supplier:]]></text>
			</staticText>
			<textField>
				<reportElement x="55" y="53" width="50" height="15" uuid="f1a2b3c4-d5e6-7f8a-9b0c-1d2e3f4a5b6c"/>
				<textElement verticalAlignment="Middle">
					<font fontName="微软雅黑" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{supplierCode}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="110" y="53" width="275" height="15" uuid="a2b3c4d5-e6f7-8a9b-0c1d-2e3f4a5b6c7d"/>
				<textElement verticalAlignment="Middle">
					<font fontName="微软雅黑" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{supplierName}]]></textFieldExpression>
			</textField>
			<rectangle>
				<reportElement x="0" y="70" width="391" height="20" uuid="b3c4d5e6-f7a8-9b0c-1d2e-3f4a5b6c7d8e"/>
				<graphicElement>
					<pen lineWidth="0.5"/>
				</graphicElement>
			</rectangle>
			<staticText>
				<reportElement x="5" y="73" width="60" height="15" uuid="c4d5e6f7-a8b9-0c1d-2e3f-4a5b6c7d8e9f">
					<printWhenExpression><![CDATA[$F{tailTag} == false]]></printWhenExpression>
				</reportElement>
				<textElement verticalAlignment="Middle">
					<font fontName="微软雅黑" size="8"/>
				</textElement>
				<text><![CDATA[Pallet QTY:]]></text>
			</staticText>
			<textField>
				<reportElement x="65" y="73" width="130" height="15" uuid="d5e6f7a8-b9c0-1d2e-3f4a-5b6c7d8e9f0a">
					<printWhenExpression><![CDATA[$F{tailTag} == false]]></printWhenExpression>
				</reportElement>
				<textElement verticalAlignment="Middle">
					<font fontName="微软雅黑" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{pallet} * $F{snp}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="200" y="73" width="50" height="15" uuid="e6f7a8b9-c0d1-2e3f-4a5b-6c7d8e9f0a1b">
					<printWhenExpression><![CDATA[$F{tailTag} == false]]></printWhenExpression>
				</reportElement>
				<textElement verticalAlignment="Middle">
					<font fontName="微软雅黑" size="8"/>
				</textElement>
				<text><![CDATA[Package:]]></text>
			</staticText>
			<textField>
				<reportElement x="250" y="73" width="135" height="15" uuid="f7a8b9c0-d1e2-3f4a-5b6c-7d8e9f0a1b2c">
					<printWhenExpression><![CDATA[$F{tailTag} == false]]></printWhenExpression>
				</reportElement>
				<textElement verticalAlignment="Middle">
					<font fontName="微软雅黑" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{pallet}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="5" y="73" width="30" height="15" uuid="a8b9c0d1-e2f3-4a5b-6c7d-8e9f0a1b2c3d">
					<printWhenExpression><![CDATA[$F{tailTag} == true]]></printWhenExpression>
				</reportElement>
				<textElement verticalAlignment="Middle">
					<font fontName="微软雅黑" size="8"/>
				</textElement>
				<text><![CDATA[QTY:]]></text>
			</staticText>
			<textField>
				<reportElement x="35" y="73" width="350" height="15" uuid="b9c0d1e2-f3a4-5b6c-7d8e-9f0a1b2c3d4e">
					<printWhenExpression><![CDATA[$F{tailTag} == true]]></printWhenExpression>
				</reportElement>
				<textElement verticalAlignment="Middle">
					<font fontName="微软雅黑" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{tailSNPQty}]]></textFieldExpression>
			</textField>
			<rectangle>
				<reportElement x="0" y="90" width="391" height="20" uuid="c0d1e2f3-a4b5-6c7d-8e9f-0a1b2c3d4e5f"/>
				<graphicElement>
					<pen lineWidth="0.5"/>
				</graphicElement>
			</rectangle>
			<staticText>
				<reportElement x="5" y="93" width="80" height="15" uuid="d1e2f3a4-b5c6-7d8e-9f0a-1b2c3d4e5f6a"/>
				<textElement verticalAlignment="Middle">
					<font fontName="微软雅黑" size="8"/>
				</textElement>
				<text><![CDATA[Delivery Date:]]></text>
			</staticText>
			<textField>
				<reportElement x="85" y="93" width="300" height="15" uuid="e2f3a4b5-c6d7-8e9f-0a1b-2c3d4e5f6a7b"/>
				<textElement verticalAlignment="Middle">
					<font fontName="微软雅黑" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{dueDate}]]></textFieldExpression>
			</textField>
			<rectangle>
				<reportElement x="0" y="110" width="130" height="60" uuid="f3a4b5c6-d7e8-9f0a-1b2c-3d4e5f6a7b8c"/>
				<graphicElement>
					<pen lineWidth="0.5"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="130" y="110" width="131" height="60" uuid="a4b5c6d7-e8f9-0a1b-2c3d-4e5f6a7b8c9d"/>
				<graphicElement>
					<pen lineWidth="0.5"/>
				</graphicElement>
			</rectangle>
			<rectangle>
				<reportElement x="261" y="110" width="130" height="60" uuid="b5c6d7e8-f9a0-1b2c-3d4e-5f6a7b8c9d0e"/>
				<graphicElement>
					<pen lineWidth="0.5"/>
				</graphicElement>
			</rectangle>
			<staticText>
				<reportElement x="5" y="115" width="80" height="15" uuid="c6d7e8f9-a0b1-2c3d-4e5f-6a7b8c9d0e1f"/>
				<textElement verticalAlignment="Top">
					<font fontName="微软雅黑" size="8"/>
				</textElement>
				<text><![CDATA[Order Number:]]></text>
			</staticText>
			<textField>
				<reportElement x="5" y="130" width="60" height="15" uuid="d7e8f9a0-b1c2-3d4e-5f6a-7b8c9d0e1f2a"/>
				<textElement verticalAlignment="Middle">
					<font fontName="微软雅黑" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{orderNo}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="65" y="130" width="5" height="15" uuid="e8f9a0b1-c2d3-4e5f-6a7b-8c9d0e1f2a3b"/>
				<textElement verticalAlignment="Middle">
					<font fontName="微软雅黑" size="8"/>
				</textElement>
				<text><![CDATA[-]]></text>
			</staticText>
			<textField>
				<reportElement x="70" y="130" width="55" height="15" uuid="f9a0b1c2-d3e4-5f6a-7b8c-9d0e1f2a3b4c"/>
				<textElement verticalAlignment="Middle">
					<font fontName="微软雅黑" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{orderLineNo}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="135" y="115" width="70" height="15" uuid="a0b1c2d3-e4f5-6a7b-8c9d-0e1f2a3b4c5d"/>
				<textElement verticalAlignment="Top">
					<font fontName="微软雅黑" size="8"/>
				</textElement>
				<text><![CDATA[Site Loc No:]]></text>
			</staticText>
			<textField>
				<reportElement x="135" y="130" width="50" height="15" uuid="b1c2d3e4-f5a6-7b8c-9d0e-1f2a3b4c5d6e"/>
				<textElement verticalAlignment="Middle">
					<font fontName="微软雅黑" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{factoryCode}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="190" y="130" width="66" height="15" uuid="c2d3e4f5-a6b7-8c9d-0e1f-2a3b4c5d6e7f"/>
				<textElement verticalAlignment="Middle">
					<font fontName="微软雅黑" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{deliveryAddress}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="266" y="115" width="80" height="15" uuid="d3e4f5a6-b7c8-9d0e-1f2a-3b4c5d6e7f8a"/>
				<textElement verticalAlignment="Top">
					<font fontName="微软雅黑" size="8"/>
				</textElement>
				<text><![CDATA[No of The Label:]]></text>
			</staticText>
			<textField>
				<reportElement x="266" y="130" width="120" height="15" uuid="e4f5a6b7-c8d9-0e1f-2a3b-4c5d6e7f8a9b"/>
				<textElement verticalAlignment="Middle">
					<font fontName="微软雅黑" size="8"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{label}]]></textFieldExpression>
			</textField>
		</band>
	</detail>
</jasperReport>
