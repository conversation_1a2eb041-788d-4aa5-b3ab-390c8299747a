package com.datalink.datamanage.domain;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import com.datalink.common.DataType;
import com.datalink.common.annotation.ExcelFieldOverride;
import com.datalink.common.annotation.Excels;
import com.datalink.kafka.KafkaData;
import com.fasterxml.jackson.annotation.JsonAlias;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.datalink.common.annotation.Excel;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Size;

/**
 * 收货反馈对象 tbl_feedback
 *
 * <AUTHOR>
 * @date 2021-07-06
 */
@ExcelFieldOverride({
    @ExcelFieldOverride.FieldOverride(fieldName = "createTime", name = "接收日期", sort = 3, dateFormat = "yyyy-MM-dd"),
    @ExcelFieldOverride.FieldOverride(fieldName = "suppCode", name = "供应商代码", sort = 4),
    @ExcelFieldOverride.FieldOverride(fieldName = "suppName", name = "供应商名称", sort = 5),
    @ExcelFieldOverride.FieldOverride(fieldName = "compCode", name = "公司代码", sort = 6),
    @ExcelFieldOverride.FieldOverride(fieldName = "compName", name = "公司名称", sort = 7),
    @ExcelFieldOverride.FieldOverride(fieldName = "plantCode", isShow = false, sort = 27)
})
public class TblFeedback  extends BaseHeadEntity implements KafkaData
{
    private static final long serialVersionUID = 1L;

    /** 收货反馈ID */
    private Long feedId;

    /** 送货单号 */
    @Excel(name = "结算单编码", sort = 1)
    @ApiModelProperty(value = "结算单编码")
    @JsonAlias("dnno")
    private String dnNo;

    /** 删除标识 */
//    @Excel(name = "删除标识")
    @ApiModelProperty(value = "删除标识")
    private String delFlag;

    /** 公司名称 */
    @Excel(name = "公司名称")
    private String compName;

    /** 凭证日期 */
//    @Excel(name = "凭证日期")
    private String docDate;

    /** 总金额 */
    @Excel(name = "开票金额", sort = 15)
    @ApiModelProperty(value = "开票金额")
    private String totalAmount;

    /** 货币 */
    @Excel(name = "币种", sort = 13)
    @ApiModelProperty(value = "币种")
    private String currency;

    /** 结算单号 */
//    @Excel(name = "结算单号")
    private String settlementNo;

    /** 开票日期 */
    @Excel(name = "开票日期", dateFormat = "yyyy-MM-dd", sort = 24)
    @JsonFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty(value = "开票日期")
    private Date invoiceDate;

    /** 开票总税额 */
    @Excel(name = "增值税总额", sort = 26)
    @ApiModelProperty(value = "增值税总额")
    private BigDecimal invoiceTax;

    /** 金税发票号 */
    @Excel(name = "金税发票号", sort = 25)
    @ApiModelProperty(value = "金税发票号")
    private String invoiceNo;

    private String depot;
    private String receivingPlace;
    private Date receivingDate;
    private BigDecimal receivingQuantity;
    private String orderUnit;
    private Date deliveryNoteDate;
    private Date deliveryNoteTime;

    /** 收发标志 */
    private String direction;

    /** KafKa发送状态 */
    private String kafkaStatus;

    /** 验收状态 */
//    @Excel(name = "验收状态")
    @ApiModelProperty(value = "状态")
    private String status;

    /** 确认时间 */
    @ApiModelProperty(value = "发送时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date confirmTime;

    /** 确认用户 */
    private String confirmBy;
    
    /** 下载状态 */
//    @Excel(name = "下载状态")
    @ApiModelProperty(value = "下载状态")
    private String downloadStatus;
    
    /** 最近下载时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
//    @Excel(name = "最近下载时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "最近下载时间")
    private Date lastDownloadTime;

    /** 收货反馈行项目信息 */
    @Excels({
        @Excel(name = "序号", targetAttr = "seqNo", sort = 2),
        @Excel(name = "零件号", targetAttr = "articleNo", sort = 8),
        @Excel(name = "零件描述", targetAttr = "articleName", sort = 9),
        @Excel(name = "数量", targetAttr = "quantity", sort = 10),
        @Excel(name = "单位", targetAttr = "unit", sort = 11),
        @Excel(name = "单价", targetAttr = "unitPrice", sort = 12),
        @Excel(name = "总金额", targetAttr = "itemAmount", sort = 14),
        @Excel(name = "税码", targetAttr = "taxCode", sort = 16),
        @Excel(name = "订单号", targetAttr = "orderCode", sort = 17),
        @Excel(name = "行号", targetAttr = "orderLineNo", sort = 18),
        @Excel(name = "工厂", targetAttr = "plantCode", sort = 19),
        @Excel(name = "接收凭证号", targetAttr = "rcvDocNo", sort = 20),
        @Excel(name = "凭证行号", targetAttr = "rcvDocItemNo", sort = 21),
        @Excel(name = "凭证年度", targetAttr = "articleDocAnnual", sort = 22),
        @Excel(name = "凭证日期", targetAttr = "rcvDate", dateFormat = "yyyyMMdd", sort = 23)
    })
    private List<TblFeedbackItem> detail;

    public void setFeedId(Long feedId)
    {
        this.feedId = feedId;
    }

    public Long getFeedId()
    {
        return feedId;
    }
    public void setDnNo(String dnNo)
    {
        this.dnNo = dnNo;
    }

    public String getDnNo()
    {
        return dnNo;
    }

    public void setDelFlag(String delFlag)
    {
        this.delFlag = delFlag;
    }

    public String getDelFlag()
    {
        return delFlag;
    }

    public void setCompName(String compName)
    {
        this.compName = compName;
    }

    public String getCompName()
    {
        return compName;
    }

    public void setDocDate(String docDate)
    {
        this.docDate = docDate;
    }

    public String getDocDate()
    {
        return docDate;
    }

    public void setTotalAmount(String totalAmount)
    {
        this.totalAmount = totalAmount;
    }

    public String getTotalAmount()
    {
        return totalAmount;
    }

    public void setCurrency(String currency)
    {
        this.currency = currency;
    }

    public String getCurrency()
    {
        return currency;
    }

    public String getSettlementNo() {
        return settlementNo;
    }

    public void setSettlementNo(String settlementNo) {
        this.settlementNo = settlementNo;
    }

    public Date getInvoiceDate() {
        return invoiceDate;
    }

    public void setInvoiceDate(Date invoiceDate) {
        this.invoiceDate = invoiceDate;
    }

    public BigDecimal getInvoiceTax() {
        return invoiceTax;
    }

    public void setInvoiceTax(BigDecimal invoiceTax) {
        this.invoiceTax = invoiceTax;
    }

    public String getInvoiceNo() {
        return invoiceNo;
    }

    public void setInvoiceNo(String invoiceNo) {
        this.invoiceNo = invoiceNo;
    }

    public String getDepot() {
        return depot;
    }

    public void setDepot(String depot) {
        this.depot = depot;
    }

    public String getReceivingPlace() {
        return receivingPlace;
    }

    public void setReceivingPlace(String receivingPlace) {
        this.receivingPlace = receivingPlace;
    }

    public Date getReceivingDate() {
        return receivingDate;
    }

    public void setReceivingDate(Date receivingDate) {
        this.receivingDate = receivingDate;
    }

    public BigDecimal getReceivingQuantity() {
        return receivingQuantity;
    }

    public void setReceivingQuantity(BigDecimal receivingQuantity) {
        this.receivingQuantity = receivingQuantity;
    }

    public String getOrderUnit() {
        return orderUnit;
    }

    public void setOrderUnit(String orderUnit) {
        this.orderUnit = orderUnit;
    }

    public Date getDeliveryNoteDate() {
        return deliveryNoteDate;
    }

    public void setDeliveryNoteDate(Date deliveryNoteDate) {
        this.deliveryNoteDate = deliveryNoteDate;
    }

    public Date getDeliveryNoteTime() {
        return deliveryNoteTime;
    }

    public void setDeliveryNoteTime(Date deliveryNoteTime) {
        this.deliveryNoteTime = deliveryNoteTime;
    }

    public void setDirection(String direction)
    {
        this.direction = direction;
    }

    public String getDirection()
    {
        return direction;
    }

    @Override
    public DataType getObjectType() {
        return DataType.FEEDBACK_TYPE;
    }

    public void setKafkaStatus(String kafkaStatus)
    {
        this.kafkaStatus = kafkaStatus;
    }

    public String getKafkaStatus()
    {
        return kafkaStatus;
    }

    public void setStatus(String status)
    {
        this.status = status;
    }

    public String getStatus()
    {
        return status;
    }

    public Date getConfirmTime() {
        return confirmTime;
    }

    public void setConfirmTime(Date confirmTime) {
        this.confirmTime = confirmTime;
    }

    public String getConfirmBy() {
        return confirmBy;
    }

    public void setConfirmBy(String confirmBy) {
        this.confirmBy = confirmBy;
    }
    
    public String getDownloadStatus() {
        return downloadStatus;
    }

    public void setDownloadStatus(String downloadStatus) {
        this.downloadStatus = downloadStatus;
    }

    public Date getLastDownloadTime() {
        return lastDownloadTime;
    }

    public void setLastDownloadTime(Date lastDownloadTime) {
        this.lastDownloadTime = lastDownloadTime;
    }

    public List<TblFeedbackItem> getDetail()
    {
        return detail;
    }

    public void setDetail(List<TblFeedbackItem> detail)
    {
        this.detail = detail;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("feedId", getFeedId())
            .append("dnNo", getDnNo())
            .append("delFlag", getDelFlag())
            .append("compCode", getCompCode())
            .append("compName", getCompName())
            .append("plantCode", getPlantCode())
            .append("plantName", getPlantName())
            .append("suppCode", getSuppCode())
            .append("suppName", getSuppName())
            .append("docDate", getDocDate())
            .append("totalAmount", getTotalAmount())
            .append("currency", getCurrency())
            .append("settlementNo", getSettlementNo())
            .append("invoiceDate", getInvoiceDate())
            .append("invoiceTax", getInvoiceTax())
            .append("invoiceNo", getInvoiceNo())
            .append("createTime", getCreateTime())
            .append("createBy", getCreateBy())
            .append("updateTime", getUpdateTime())
            .append("updateBy", getUpdateBy())
            .append("direction", getDirection())
            .append("kafkaStatus", getKafkaStatus())
            .append("status", getStatus())
            .append("downloadStatus", getDownloadStatus())
            .append("lastDownloadTime", getLastDownloadTime())
            .append("tblFeedbackItemList", getDetail())
            .toString();
    }
}