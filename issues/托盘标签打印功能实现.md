# 托盘标签打印功能实现

## 任务概述
参照Vue.js老系统的托盘标签样式，修改TblAsnServiceImpl中的printPalletTag方法，实现托盘标签打印功能。

## 实现内容

### 1. 修改TblAsnServiceImpl.java
**文件路径**: `datalink-data-manage/src/main/java/com/datalink/datamanage/service/impl/TblAsnServiceImpl.java`

#### 1.1 修改printPalletTag方法
- 将调用的方法从`genPrintDataForProdTag`改为`genPrintDataForPalletTag`
- 保持其他逻辑不变（ASN存在性检查、发送状态检查等）

#### 1.2 新增genPrintDataForPalletTag方法
**功能**: 生成托盘标签打印数据
**逻辑**:
1. 遍历ASN的所有物料
2. 获取每个物料的SNP信息和托盘配置
3. 根据托盘配置计算托盘数量
4. 为每个托盘生成标签数据，区分正常托盘和尾托盘
5. 返回header和data格式的Map

**数据字段**:
- 基本信息: partNo, partDescription, supplierCode, supplierName
- 托盘信息: pallet, snp, tailTag, tailSNPQty
- 订单信息: orderNo, orderLineNo, dueDate
- 地址信息: factoryCode, deliveryAddress
- 标签信息: label

### 2. 创建pallet.jrxml模板
**文件路径**: `datalink-data-manage/src/main/resources/templates/pallet.jrxml`

#### 2.1 模板特性
- 使用规范的UUID标识符
- 支持2列水平优先布局（columnCount="2" printOrder="Horizontal"）
- 参考Vue.js模板的样式和布局
- 包含Marelli Logo（Base64编码）

#### 2.2 布局结构
1. **Part No行**: 显示零件号和Logo
2. **PartName行**: 显示零件描述
3. **Supplier行**: 显示供应商代码和名称
4. **数量信息行**: 根据是否为尾托盘显示不同内容
   - 正常托盘: Pallet QTY (pallet * snp) 和 Package (pallet)
   - 尾托盘: QTY (tailSNPQty)
5. **Delivery Date行**: 显示交货日期
6. **底部三列**:
   - Order Number: orderNo - orderLineNo
   - Site Loc No: factoryCode deliveryAddress
   - No of The Label: label

#### 2.3 条件显示
使用`printWhenExpression`根据`tailTag`字段控制显示内容：
- `$F{tailTag} == false`: 显示正常托盘信息
- `$F{tailTag} == true`: 显示尾托盘信息

### 3. Controller验证
**文件路径**: `datalink-data-manage/src/main/java/com/datalink/datamanage/controller/TblAsnController.java`
- 确认printPalletTag方法调用正确的service方法
- 方法映射: `/printPalletTag`

## 技术要点

### 1. 托盘数据计算
- 基于现有的`TblAsnArticle.Pallet`数据结构
- 利用物料的SNP信息和托盘配置
- 支持尾托盘的特殊处理

### 2. JasperReports模板
- 使用标准UUID格式避免编译错误
- 支持条件显示不同的托盘信息
- 保持与老系统一致的视觉效果

### 3. 数据流程
```
ASN数据 -> 托盘配置查询 -> 托盘数量计算 -> 标签数据生成 -> JasperReports渲染 -> PDF输出
```

## 测试建议
1. 验证有托盘配置的物料打印效果
2. 验证尾托盘的显示逻辑
3. 验证2列布局的排版效果
4. 验证Logo和样式的显示

## 完成状态
- [x] 修改printPalletTag方法调用
- [x] 实现genPrintDataForPalletTag方法
- [x] 创建pallet.jrxml模板
- [x] 修正UUID格式问题
- [x] 验证Controller方法映射

## 注意事项
1. 模板中的UUID必须使用标准格式
2. 托盘配置为空时不生成标签数据
3. 支持水平优先的2列布局
4. Logo使用Base64编码嵌入模板
