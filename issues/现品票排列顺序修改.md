# 现品票排列顺序修改任务

## 任务描述
将现品票的排列顺序从"先竖排再横排"改为"先横排再竖排"

## 问题分析
- 原始配置：`columnCount="2"` + 默认 `printOrder="Vertical"`
- 效果：先填满第一列，再填第二列（先竖排再横排）
- 需求：先填满第一行，再填第二行（先横排再竖排）

## 解决方案
在 JasperReports 模板中添加 `printOrder="Horizontal"` 属性

## 修改内容
**文件**: `datalink-data-manage/src/main/resources/templates/xpp.jrxml`

**修改前**:
```xml
<jasperReport ... columnCount="2" ... uuid="...">
```

**修改后**:
```xml
<jasperReport ... columnCount="2" ... printOrder="Horizontal" uuid="...">
```

## 修改效果
- 保持现品票的样式和内容不变
- 仅改变多个现品票的排列顺序
- 现在会先填满第一行的两个位置，然后填第二行

## 完成时间
2025-07-02

## 状态
✅ 已完成
