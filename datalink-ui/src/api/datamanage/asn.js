import request from '@/utils/request'

// 查询ASN列表
export function listAsn(query) {
  return request({
    url: '/datamanage/asn/list',
    method: 'get',
    params: query
  })
}

// 查询ASN详细
export function getAsn(asnid) {
  return request({
    url: '/datamanage/asn/' + asnid,
    method: 'get'
  })
}

// 新增ASN
export function addAsn(data) {
  return request({
    url: '/datamanage/asn',
    method: 'post',
    data: data
  })
}

// 修改ASN
export function updateAsn(data) {
  return request({
    url: '/datamanage/asn',
    method: 'put',
    data: data
  })
}

// 删除ASN
export function delAsn(asnid) {
  return request({
    url: '/datamanage/asn/' + asnid,
    method: 'delete'
  })
}

// 导出ASN
export function exportAsn(query) {
  return request({
    url: '/datamanage/asn/export',
    method: 'get',
    params: query
  })
}

// 查询ASN列表
export function listAsnItem(query) {
  return request({
    url: '/datamanage/asn/listItems',
    method: 'get',
    params: query
  })
}

// 查询ASN列表
export function listAsnArticles(query) {
  return request({
    url: '/datamanage/asn/listArticles',
    method: 'get',
    params: query
  })
}

// 查询ASN详细(不包含行项目)
export function getAsnOnly(asnId) {
  return request({
    url: '/datamanage/asn/head/' + asnId,
    method: 'get'
  })
}

// 获取订单剩余物料列表
export function listOrderAsnQuantities(query) {
  return request({
    url: '/datamanage/asn/listOrderAsnQuantities',
    method: 'get',
    params: query
  })
}

// 打印条码
export function printAsnCode(asnId, tz) {
  return request({
    url: `/datamanage/asn/printPdf?asnId=${asnId}&tz=${tz}`,
    method: 'get'
  })
}

export function printPickingListByAsnCode(asnId, tz) {
  return request({
    url: `/datamanage/asn/printPickList?asnId=${asnId}&tz=${tz}`,
    method: 'get'
  })
}

export function printNpsSls(asnId) {
  return request({
    url: `/datamanage/asn/printNpsSls?asnId=${asnId}`,
    method: 'get'
  })
}

export function printProdTag(asnId) {
  return request({
    url: `/datamanage/asn/printProdTag?asnId=${asnId}`,
    method: 'get'
  })
}
