import request from '@/utils/request'

// 查询列表
export function listPallet(query) {
  return request({
    url: '/datamanage/pallet/list',
    method: 'get',
    params: query
  })
}

export function upsertPallet(data) {
  return request({
    url: '/datamanage/pallet/save',
    method: 'post',
    data
  })
}

export function deletePallet(palletId) {
  return request({
    url: `/datamanage/pallet/${palletId}`,
    method: 'delete'
  })
}
