import request from '@/utils/request'

// 查询收货反馈列表
export function listFeedback(query) {
  return request({
    url: '/datamanage/feedback/list',
    method: 'get',
    params: query
  })
}

// 查询收货反馈详细
export function getFeedback(feedid) {
  return request({
    url: '/datamanage/feedback/' + feedid,
    method: 'get'
  })
}

// 查询收货反馈详细（不包含行项目）
export function getFeedbackOnly(feedid) {
  return request({
    url: '/datamanage/feedback/head/' + feedid,
    method: 'get'
  })
}

// 新增收货反馈
export function addFeedback(data) {
  return request({
    url: '/datamanage/feedback',
    method: 'post',
    data: data
  })
}

// 修改收货反馈
export function updateFeedback(data) {
  return request({
    url: '/datamanage/feedback',
    method: 'put',
    data: data
  })
}

// 删除收货反馈
export function delFeedback(feedid) {
  return request({
    url: '/datamanage/feedback/' + feedid,
    method: 'delete'
  })
}

// 导出收货反馈
export function exportFeedback(query) {
  return request({
    url: '/datamanage/feedback/export',
    method: 'get',
    params: query
  })
}

// 查询收货反馈列表
export function listFeedbackItems(query) {
  return request({
    url: '/datamanage/feedback/listItems',
    method: 'get',
    params: query
  })
}

export function printTxt(ids) {
  return request({
    url: '/datamanage/feedback/downloadFeedbackTxt',
    method: 'post',
    data: ids
  })
}

export function downloadExcel(ids) {
  return request({
    url: '/datamanage/feedback/downloadExcel',
    method: 'post',
    data: ids
  })
}

export function updateInvoiceInfo(data) {
  return request({
    url: '/datamanage/feedback/updateInvoiceInfo',
    method: 'post',
    data,
  })
}

export function confirmFeedback(ids) {
  return request({
    url: '/datamanage/feedback/confirm',
    method: 'post',
    data: ids
  })
}

