export default {
  acceptanceDetailOverview: {
    form: {
      suppCode: '供应商代码',
      dateRange: '日期范围'
    },
    placeholder: {
      suppCode: '请输入供应商代码',
      startDate: '开始日期',
      endDate: '结束日期'
    },
    table: {
      settlementNo: '结算单编码',
      suppCode: '供应商代码',
      suppName: '供应商名称',
      companyCode: '公司代码',
      operateTime: '操作时间',
      operateType: '操作类型',
      operator: '操作人'
    }
  },
  forecastOverview: {
    form: {
      suppCode: '供应商代码',
      dateRange: '日期范围'
    },
    placeholder: {
      suppCode: '请输入供应商代码',
      startDate: '开始日期',
      endDate: '结束日期'
    },
    table: {
      sendTime: '发送时间',
      suppCode: '供应商代码',
      suppName: '供应商名称',
      plant: '工厂',
      operateTime: '操作时间',
      operateType: '操作类型',
      operator: '操作人'
    }
  },
  orderOverview: {
    form: {
      suppCode: '供应商代码',
      plant: '工厂',
      dateRange: '日期范围'
    },
    placeholder: {
      suppCode: '请输入供应商代码',
      plant: '请输入工厂',
      startDate: '开始日期',
      endDate: '结束日期'
    },
    table: {
      suppCode: '供应商代码',
      suppName: '供应商名称',
      orderNo: '订单号',
      plant: '工厂',
      operateTime: '操作时间',
      operateType: '操作类型',
      operator: '操作人'
    }
  },
  palletMaintain: {
    updatePalletInfo: '更新托盘信息',
    form: {
      plant: '工厂',
      partNo: '零件号',
      plantCode: '工厂代码',
      materialCode: '零件代码'
    },
    placeholder: {
      plant: '请输入工厂',
      partNo: '请输入零件号',
      plantCode: '请输入工厂代码',
      materialCode: '请输入零件代码'
    },
    button: {
      search: '搜索',
      reset: '重置',
      edit: '编辑',
      save: '保存',
      cancel: '取消',
      delete: '删除',
      create: '新增',
      confirm: '确定'
    },
    table: {
      index: '序号',
      action: '操作',
      plant: '工厂代码',
      plantCode: '工厂',
      partNo: '零件号',
      materialCode: '零件代码',
      partDesc: '零件描述',
      qtyPerPack: '单位包装数',
      palletSnpQty: '整托包含SNP数量',
      palletSnpQtyPlaceholder: '请输入整托包含SNP数量',
      palletLength: '托长',
      palletLengthPlaceholder: '请输入托长',
      palletWidth: '托宽',
      palletWidthPlaceholder: '请输入托宽',
      palletHeight: '托高',
      palletHeightPlaceholder: '请输入托高',
      containerType: '容器种类',
      containerTypePlaceholder: '请输入容器种类'
    },
    alert: {
      saveBeforeEdit: '请先保存或取消当前编辑行',
      saveSuccess: '保存成功'
    },
    confirm: {
      deletePallet: '确定要删除托盘信息吗？'
    },
    message: {
      upsertSuccess: '更新托盘成功'
    }
  },
  internalForecastDownloadNew: {
    form: {
      suppCode: '供应商代码',
      createTime: '创建时间',
      status: '状态'
    },
    placeholder: {
      suppCode: '请输入供应商代码',
      status: '请选择状态',
      startTime: '开始时间',
      endTime: '结束时间'
    },
    button: {
      search: '查询',
      reset: '重置',
      printSelectedRows: '打印选中行',
      confirmSelectedRows: '确认选中行'
    },
    table: {
      index: '序号',
      createTime: '创建时间',
      suppCode: '供应商代码',
      plantCode: '工厂',
      status: '状态',
      downloadingStatus: '下载状态',
      lastDownloadingTime: '最后下载时间'
    },
    status: {
      notDownloaded: '未下载',
      downloaded: '已下载',
      normal: '正常',
      abnormal: '异常'
    },
    alert: {
      selectOrder: '请先选择需要操作的行',
      printSuccess: '打印成功',
      confirmSuccess: '确认成功'
    },
    common: {
      loading: '加载中...'
    }
  },
  deliveryNoteGenerate: {
    form: {
      orderNo: '订单号',
      suppCode: '供应商代码',
      shipStatus: '货运状态',
      dateRange: '日期范围'
    },
    placeholder: {
      orderNo: '请输入订单号',
      suppCode: '请输入供应商代码',
      shipStatus: '请选择货运状态',
      startTime: '开始日期',
      endTime: '结束日期'
    },
    button: {
      generate: '生成纳品书/受领书',
      print: '打印纳品书/受领书',
      edit: '编辑',
      save: '保存',
      cancel: '取消'
    },
    table: {
      index: '序号',
      action: '操作',
      orderNo: '订单号',
      lineNo: '行号',
      releaseNo: '下达号',
      partNo: '零件号',
      partDesc: '零件描述',
      orderQty: '订单数量',
      deliveryQty: '交货数量',
      totalDeliveryQty: '总交货数量',
      qtyPerPack: '单位包装数',
      deliveryDate: '发货日期',
      plant: '工厂',
      unit: '单位'
    },
    alert: {
      selectRow: '请先选择要操作的行',
      generateSuccess: '生成成功',
      printSuccess: '打印成功',
      saveBeforeEdit: '请先保存当前编辑行',
      saveSuccess: '保存成功'
    }
  },
  acceptanceDownload: {
    form: {
      suppCode: '供应商代码',
      status: '状态',
      dateRange: '时间范围'
    },
    placeholder: {
      suppCode: '请输入供应商代码',
      status: '请选择状态',
      startTime: '开始时间',
      endTime: '结束时间'
    },
    button: {
      downloadSelectedRows: '下载选中行',
      confirmSelectedRows: '确认选中行',
      edit: '编辑',
      save: '保存',
      cancel: '取消'
    },
    table: {
      index: 'NO',
      action: '操作',
      settlementCode: '结算单编码',
      suppName: '供应商',
      compCode: '公司代码',
      status: '状态',
      invoiceTotalTax: '开票总税额',
      invoiceNo: '金税发票号',
      invoiceDate: '开票日期',
      invoiceAmount: '开票金额',
      currency: '币种',
      receiveDate: '接收日期'
    },
    alert: {
      selectRow: '请先选择要操作的行',
      downloadSuccess: '下载成功',
      confirmSuccess: '确认成功',
      saveBeforeEdit: '请先保存当前编辑行',
      saveSuccess: '保存成功'
    }
  },
  ticketPrint: {
    form: {
      orderNo: '订单号',
      suppCode: '供应商代码',
      shipStatus: '货运状态',
      dateRange: '日期范围'
    },
    placeholder: {
      orderNo: '请输入订单号',
      suppCode: '请输入供应商代码',
      shipStatus: '请选择货运状态',
      startTime: '开始日期',
      endTime: '结束日期'
    },
    button: {
      printDeliveryAcceptance: '打印纳品书/受领书',
      printPickList: '打印提货单',
      printItemTag: '打印现品票',
      printPalletTag: '打印托标签'
    },
    table: {
      index: '序号',
      asnNo: 'ASN号',
      orderNo: '订单号',
      lineNo: '行号',
      releaseNo: '下达号',
      partNo: '零件号',
      partDesc: '零件描述',
      plant: '工厂',
      shipDate: '发货日期',
      orderQty: '订单数量',
      deliveryQty: '交货数量',
      qtyPerPack: '单位包装数',
      unit: '单位',
      shipStatus: '货运状态',
      deliveryDate: '收货日期',
      warehouse: '仓库',
      palletCount: '托盘数',
      containerType: '容器类型',
      totalDeliveryQty: '总交货数量'
    },
    alert: {
      selectRow: '请先选择要操作的行',
      printSuccess: '打印成功'
    },
    dialog: {
      itemTagTitle: '打印现品票',
      lineNo: '行号',
      orderNo: '订单号',
      batchNo: '批次号',
      addBatchNo: '增加批号',
      removeBatchNo: '删除批号',
      printItemTag: '打印现品票',
      reprintItemTag: '补打现品票',
      selectReprintRow: '请选择需要补打的序号',
      reprintSuccess: '补打现品票成功',
      inputLineOrder: '请输入行号和订单号',
      noBatchToDelete: '没有可删除的批次',
      table: {
        index: '序号',
        orderNo: '订单号',
        lineNo: '行号',
        releaseNo: '下达号',
        partNo: '零件号',
        partDesc: '零件描述',
        batchNo: '批次',
        snp: 'SNP',
        deliveryQty: '交货数量',
        deliveryDate: '交货日期'
      }
    }
  },
  orderDetail: {
    form: {
      orderNo: '订单号',
      partNo: '零件号',
      shipmentStatus: '货运状态',
      orderDate: '下单日期',
      startDate: '开始日期',
      endDate: '结束日期'
    },
    placeholder: {
      orderNo: '请输入订单号',
      partNo: '请输入零件号',
      shipmentStatus: '请选择货运状态',
      startDate: '开始日期',
      endDate: '结束日期'
    },
    button: {
      search: '搜索',
      reset: '重置'
    },
    table: {
      orderNo: '订单号',
      lineNo: '行号',
      releaseNo: '下达号',
      orderStatus: '订单状态',
      shipmentStatus: '货运状态',
      partNo: '零件号',
      partDesc: '零件描述',
      deliveryDate: '交货日期',
      orderQty: '订单数量',
      unit: '单位'
    },
    shipmentStatusOptions: {
      not_shipped: '未发货',
      shipping: '运输中',
      delivered: '已到货'
    }
  },

  web: {
    title: 'DataLink管理系统'
  },
  loadingMessage: '正在加载系统资源，请耐心等待',
  dashboard: {
    title: '七日接收数据汇总',
    stats: {
      Order: '订单',
      Forecast: '预测',
      Consignment: '寄售库存',
      Inventory: '库存',
      Feedback: '收货反馈'
    },
    orderAnalysis: '七日接收数据汇总',
    totalOrders: '总订单数',
    completedOrders: '已完成订单',
    pendingOrders: '待处理订单',
    compareToYesterday: '相较昨日',
    chart: {
      totalOrders: '总订单',
      completedOrders: '已完成',
      pendingOrders: '待处理'
    }
  },
  route: {
    dashboard: '首页',
    orderDownload: '订单下载',
    dataManagement: '数据管理',
    dataReceive: '数据接收',
    order: '订单',
    forecast: '预测',
    consignmentInventory: '寄售库存',
    inventory: '库存',
    receiptFeedback: '收货反馈',
    ASN: 'ASN',
    dataSend: '数据发送',
    systemManagement: '系统管理',
    userManagement: '用户管理',
    roleManagement: '角色管理',
    menuManagement: '菜单管理',
    supplierManagement: '供应商管理',
    postManagement: '承运商管理',
    dictionaryManagement: '字典管理',
    parameterSettings: '参数设置',
    notice: '通知公告',
    logManagement: '日志管理',
    operationLog: '操作日志',
    loginLog: '登录日志',
    systemMonitoring: '系统监控',
    onlineUsers: '在线用户',
    scheduledTasks: '定时任务',
    dataMonitoring: '数据监控',
    serverMonitoring: '服务监控',
    cacheMonitoring: '缓存监控',
    systemTools: '系统工具',
    formBuilder: '表单构建',
    codeGeneration: '代码生成',
    systemInterface: '系统接口',
    profile: '个人中心',
    dictData: '字典数据',
    dispatchLog: '调度日志',
    modifyGeneratedConfig: '修改生成配置',
    orderDetail: '订单详情',
    purchaseOrderDetail: '采购订单详情',
    acceptanceDownload: '验收明细下载',
    deliveryNoteGenerate: '生成纳品书/受领书',
    ticketPrint: '票据打印',
    palletMaintain: '托盘维护',
    orderOverview: '订单概览',
    forecastDetail: '预测详情',
    forecastOverview: '内部预测概览',
    acceptanceDetailOverview: '验收明细概览',
    consignmentInventoryDetail: '寄售库存详情',
    inventoryDetail: '库存详情',
    receiptFeedbackDetail: '收货反馈详情',
    ASNDetail: 'ASN详情',
    ASNEdit: 'ASN编辑',
    internalForecast: '内部预测',
    loadProposalVehicleRegistration: '货量提示配车登记',
    loadProposalVehicleConfirmation: '货量提示配车确认',
    supplyPlan: '供应计划',
    kanban: '单机能连携',
    ePartner: 'e-Partner',
    kanbaA8245: '单机能连携数据(生产线)',
    kanbaM4028: '单机能连携数据(SV)',
    kanbaN2396: '单机能连携数据(KD)',
    downloadFunction: '下载功能',
    printFunction: '打印功能',
    overview: '概览'
  },
  order: {
    form: {
      orderCode: '订单编号',
      compCode: '公司代码',
      compName: '公司名称',
      suppCode: '供应商代码',
      suppName: '供应商名称',
      plantCode: '工厂代码',
      plantName: '工厂名称',
      plannerNo: '采购组',
      plannerName: '采购组描述',
      createTime: '创建时间',
      sapUpdateTime: '更新时间',
      receiveTime: '接收时间',
      isRead: '是否已阅',
      isComplete: '是否完成',
      timeBegin: '时间窗开始',
      timeEnd: '时间窗结束',
      itemNo: '行号',
      purDocType: '采购凭证类型',
      itemType: '项目类别',
      text: '采购订单头',
      delIden: '删除标识',
      shortText: '采购物料描述',
      oldArticleNo: '旧物料号',
      articleNo: '物料号',
      articleName: '物料名称',
      deliveryDate: '交货日期',
      quantity: '数量',
      unit: '单位',
      workbinNo: '箱型',
      workbinName: '箱型描述',
      qtyPerPack: '标准包装数量',
      unloadingNo: '卸货地点',
      unloadingName: '卸货方',
      state: '状态',
      netPrice: '采购价格',
      priceUnit: '价格单位',
      orderNetWorth: '订单净值',
      currencyCode: '货币',
      stockLoc: '库存地点',
      locDes: '库存地点描述',
      locAdd: '库存地点位置',
      rcvName: '收货人姓名',
      rcvTel: '收货人电话',
      inspeStrategy: '检验策略',
      zipCode: '邮政编码',
      city: '城市',
      countryCode: '国家代码',
      addTimeZone: '地址时区',
      street2: '街道2',
      street3: '街道3',
      street4: '街道4',
      depot: '仓库'
    },
    placeholder: {
      orderCode: '请输入订单编号',
      compCode: '请输入公司代码',
      compName: '请输入公司名称',
      suppCode: '请输入供应商代码',
      suppName: '请输入供应商名称',
      plantCode: '请输入工厂代码',
      plantName: '请输入工厂名称',
      plannerNo: '请输入采购组',
      plannerName: '请输入采购组描述',
      startTime: '开始时间',
      endTime: '结束时间',
      select: '请选择',
      itemNo: '请输入行号',
      purDocType: '请输入采购凭证类型',
      itemType: '请输入项目类别',
      text: '请输入采购订单头',
      delIden: '请输入删除标识',
      shortText: '请输入采购物料描述',
      oldArticleNo: '请输入旧物料号',
      articleNo: '请输入物料号',
      articleName: '请输入物料名称',
      deliveryDate: '请输入交货日期',
      quantity: '请输入数量',
      unit: '请输入单位',
      workbinNo: '请输入箱型',
      workbinName: '请输入箱型描述',
      qtyPerPack: '请输入标准包装数量',
      unloadingNo: '请输入Dock',
      unloadingName: '请输入Dock名称',
      state: '请输入状态',
      netPrice: '请输入采购价格',
      priceUnit: '请输入价格单位',
      orderNetWorth: '请输入订单净值',
      currencyCode: '请输入货币',
      stockLoc: '请输入库存地点',
      locDes: '请输入库存地点描述',
      locAdd: '请输入库存地点位置',
      rcvName: '请输入收货人姓名',
      rcvTel: '请输入收货人电话',
      inspeStrategy: '请输入检验策略',
      zipCode: '请输入邮政编码',
      city: '请输入城市',
      countryCode: '请输入国家代码',
      addTimeZone: '请输入地址时区',
      street2: '请输入街道2',
      street3: '请输入街道3',
      street4: '请输入街道4',
      depot: '请输入仓库'
    },
    table: {
      orderCode: '订单编号',
      compCode: '公司代码',
      compName: '公司名称',
      suppCode: '供应商代码',
      suppName: '供应商名称',
      downloadStatus: '下载状态',
      plantCode: '工厂代码',
      plantName: '工厂名称',
      unloadingNo: '卸货地点',
      unloadingName: '卸货方',
      plannerNo: '采购组',
      plannerName: '采购组描述',
      customerCode: '客户编号',
      requester: '送达方要求',
      createTime: '创建时间',
      sapUpdateTime: '更新时间',
      receiveTime: '接收时间',
      isRead: '是否已阅',
      isComplete: '是否完成',
      timeBegin: '时间窗开始',
      timeEnd: '时间窗结束',
      index: '序号',
      actions: '操作'
    },
    button: {
      search: '搜索',
      reset: '重置',
      createASN: '创建ASN',
      orderConfirm: '订单确认',
      printItemTag: '打印现品票',
      printTxt: '下载TXT文件',
      orderDownload: '订单下载',
      printPartsInstructionNote: '打印部品纳入指示书',
      export: '导出',
      add: '添加',
      delete: '删除',
      confirm: '确定',
      cancel: '取消',
      notification: '提示',
      warning: '警告'
    },
    divider: {
      orderItems: '订单行项目信息'
    },
    title: {
      add: '添加订单',
      update: '修改订单'
    },
    success: {
      add: '新增成功',
      update: '修改成功',
      delete: '删除成功',
      confirm: '确认成功'
    },
    confirm: {
      delete: '是否确认删除订单编号为"{orderIds}"的数据项?',
      confirmAllItems: '即使未选中所有明细，也会确认整个订单，确定吗？',
      downloadAllItems: '即使未选中所有明细，也会下载整个订单，确定吗？'
    },
    alert: {
      deleteItem: '请先选择要删除的订单行项目数据',
      selectOrder: '至少选择一条订单',
      onlyNewCanConfirm: '订单号为 {notNewOrderCodes} 的状态不是 New，无法确认',
      sameClient: '所选订单需为同一客户',
      sameSupplier: '所选订单需为同一供应商',
      samePlant: '所选订单需为同一工厂',
      sameDepot: '所选订单需为同一仓库',
      sameUnloading: '所选订单需为同一卸货地点',
      orderCompleted: '无法为已完成的订单创建ASN',
      znbsNotAllowed: '禁止操作，此订单为客户直送业务订单（卸货点ZNBS）'
    },
    rules: {
      compCode: '公司代码不能为空',
      compName: '公司名称不能为空',
      plantCode: '工厂代码不能为空',
      plantName: '工厂名称不能为空',
      suppCode: '供应商代码不能为空',
      suppName: '供应商名称不能为空',
      plannerNo: '采购组不能为空',
      plannerName: '采购组描述不能为空',
      orderCode: '订单编号不能为空'
    },
    orderDetail: {
      baseInfoTitle: '订单信息',
      itemsInfoTitle: '行项目信息',
      orderCode: '订单编号',
      compCode: '公司代码',
      compName: '公司名称',
      plantCode: '工厂代码',
      plantName: '工厂名称',
      plannerNo: '采购组',
      plannerName: '采购员',
      suppCode: '供应商代码',
      suppName: '供应商名称',
      itemNo: '行号',
      status: '订单状态',
      releaseNo: '下达号',
      articleNo: '物料号',
      articleName: '物料名称',
      quantity: '数量',
      unit: '单位',
      netPrice: '采购价格',
      priceUnit: '价格单位',
      currencyCode: '货币',
      deliveryDate: '交货日期',
      qtyPerPack: '标准包装数量',
      deliverySplit: '分割回数',
      workbinNo: '箱型',
      workbinName: '箱型描述',
      state: '状态',
      purDocType: '采购凭证类型',
      itemType: '项目类别',
      text: '采购订单头',
      orderNetWorth: '采购订单货币的订单净值',
      delIden: '采购凭证中的删除标识',
      shortText: '采购物料描述',
      oldArticleNo: '旧物料号',
      unloadingNo: '卸货地点',
      unloadingName: '卸货方',
      stockLoc: '库存地点',
      locDes: '库存地点描述',
      locAdd: '库存地点位置',
      rcvName: '收货人姓名',
      rcvTel: '收货人电话',
      inspeStrategy: '检验策略',
      zipCode: '邮政编码',
      city: '城市',
      countryCode: '国家代码',
      addTimeZone: '地址时区',
      street2: '街道2',
      street3: '街道3',
      street4: '街道4',
      title: '标题',
      content: '内容',
      customerOrderCode: '客户订单号',
      customerOrderLineCode: '客户订单行号',
      customerDeliveryDate: '客户交货日期',
      productType: '生产方式',
      rcvType: '收货方式',
      purchaseType: '自行采购类型',
      depot: '仓库'
    }
  },
  purchaseOrder: {
    orderDetail: {
      baseInfoTitle: '订单信息',
      partsInfoTitle: '零件信息',
      orderCode: '订单编号',
      lineNo: '行号',
      releaseNo: '下达号',
      suppAddr: '供应商地址',
      rcvAddr: '收货地址',
      depot: '仓库',
      plantName: '工厂',
      shipStatus: '发运状态',
      deliveryDate: '交货日期',
      partNo: '零件号',
      partDesc: '零件描述',
      quantity: '数量',
      orderStatus: '订单状态',
      unit: '单位',
      qtyPerPack: '标准包装数',
      buyerCode: '采购商编号',
      title: '标题',
      content: '内容'
    },
    form: {
      orderNo: '订单号',
      orderNoPlaceholder: '请输入订单号',
      partNo: '零件号',
      partNoPlaceholder: '请输入零件号',
      shipStatus: '货运状态',
      shipStatusPlaceholder: '请选择货运状态',
      dateRange: '日期范围',
      startDate: '开始日期',
      endDate: '结束日期',
      to: '至'
    },
    placeholder: {
      orderNo: '请输入订单号',
      partNo: '请输入零件号',
      shipStatus: '请选择货运状态',
      dateRange: '请选择日期范围',
      to: '至',
      startDate: '开始日期',
      endDate: '结束日期'
    },
    table: {
      orderNo: '订单号',
      lineNo: '行号',
      releaseNo: '下达号',
      orderStatus: '订单状态',
      shipStatus: '货运状态',
      partNo: '零件号',
      partDesc: '零件描述',
      deliveryDate: '交货日期',
      orderQty: '订单数量',
      unit: '单位'
    },
    search: '搜索',
    button: {
      search: '搜索',
      reset: '重置',
      createASN: '创建ASN',
      printItemTag: '打印现品票',
      printTxt: '下载TXT文件',
      printPartsInstructionNote: '打印部品纳入指示书',
      export: '导出',
      add: '添加',
      delete: '删除',
      confirm: '确定',
      cancel: '取消',
      notification: '提示',
      warning: '警告'
    },
    divider: {
      orderItems: '订单行项目信息'
    },
    success: {
      add: '新增成功',
      update: '修改成功',
      delete: '删除成功'
    },
    confirm: {
      delete: '是否确认删除订单编号为"{orderIds}"的数据项?'
    },
    alert: {
      deleteItem: '请先选择要删除的订单行项目数据',
      selectOrder: '至少选择一条订单',
      sameClient: '所选订单需为同一客户',
      sameSupplier: '所选订单需为同一供应商',
      samePlant: '所选订单需为同一工厂',
      sameDepot: '所选订单需为同一仓库',
      sameUnloading: '所选订单需为同一卸货地点',
      orderCompleted: '无法为已完成的订单创建ASN',
      znbsNotAllowed: '禁止操作，此订单为客户直送业务订单（卸货点ZNBS）'
    },
    rules: {
      compCode: '公司代码不能为空',
      compName: '公司名称不能为空',
      plantCode: '工厂代码不能为空',
      plantName: '工厂名称不能为空',
      suppCode: '供应商代码不能为空',
      suppName: '供应商名称不能为空',
      plannerNo: '采购组不能为空',
      plannerName: '采购组描述不能为空',
      orderCode: '订单编号不能为空'
    }
  },
  orderDownload: {
    form: {
      orderCode: '订单编号',
      compCode: '公司代码',
      compName: '公司名称',
      suppCode: '供应商代码',
      suppName: '供应商名称',
      plantCode: '工厂代码',
      plantName: '工厂名称',
      plannerNo: '采购组',
      plannerName: '采购组描述',
      createTime: '创建时间',
      sapUpdateTime: '更新时间',
      receiveTime: '接收时间',
      isRead: '是否已阅',
      isComplete: '是否完成',
      timeBegin: '时间窗开始',
      timeEnd: '时间窗结束',
      itemNo: '行号',
      purDocType: '采购凭证类型',
      itemType: '项目类别',
      text: '采购订单头',
      delIden: '删除标识',
      shortText: '采购物料描述',
      oldArticleNo: '旧物料号',
      articleNo: '物料号',
      articleName: '物料名称',
      deliveryDate: '交货日期',
      quantity: '数量',
      unit: '单位',
      workbinNo: '箱型',
      workbinName: '箱型描述',
      qtyPerPack: '标准包装数量',
      unloadingNo: '卸货地点',
      unloadingName: '卸货方',
      state: '状态',
      netPrice: '采购价格',
      priceUnit: '价格单位',
      orderNetWorth: '订单净值',
      currencyCode: '货币',
      stockLoc: '库存地点',
      locDes: '库存地点描述',
      locAdd: '库存地点位置',
      rcvName: '收货人姓名',
      rcvTel: '收货人电话',
      inspeStrategy: '检验策略',
      zipCode: '邮政编码',
      city: '城市',
      countryCode: '国家代码',
      addTimeZone: '地址时区',
      street2: '街道2',
      street3: '街道3',
      street4: '街道4',
      depot: '仓库'
    },
    placeholder: {
      orderCode: '请输入订单编号',
      compCode: '请输入公司代码',
      compName: '请输入公司名称',
      suppCode: '请输入供应商代码',
      suppName: '请输入供应商名称',
      plantCode: '请输入工厂代码',
      plantName: '请输入工厂名称',
      plannerNo: '请输入采购组',
      plannerName: '请输入采购组描述',
      startTime: '开始时间',
      endTime: '结束时间',
      select: '请选择',
      itemNo: '请输入行号',
      purDocType: '请输入采购凭证类型',
      itemType: '请输入项目类别',
      text: '请输入采购订单头',
      delIden: '请输入删除标识',
      shortText: '请输入采购物料描述',
      oldArticleNo: '请输入旧物料号',
      articleNo: '请输入物料号',
      articleName: '请输入物料名称',
      deliveryDate: '请输入交货日期',
      quantity: '请输入数量',
      unit: '请输入单位',
      workbinNo: '请输入箱型',
      workbinName: '请输入箱型描述',
      qtyPerPack: '请输入标准包装数量',
      unloadingNo: '请输入Dock',
      unloadingName: '请输入Dock名称',
      state: '请输入状态',
      netPrice: '请输入采购价格',
      priceUnit: '请输入价格单位',
      orderNetWorth: '请输入订单净值',
      currencyCode: '请输入货币',
      stockLoc: '请输入库存地点',
      locDes: '请输入库存地点描述',
      locAdd: '请输入库存地点位置',
      rcvName: '请输入收货人姓名',
      rcvTel: '请输入收货人电话',
      inspeStrategy: '请输入检验策略',
      zipCode: '请输入邮政编码',
      city: '请输入城市',
      countryCode: '请输入国家代码',
      addTimeZone: '请输入地址时区',
      street2: '请输入街道2',
      street3: '请输入街道3',
      street4: '请输入街道4',
      depot: '请输入仓库'
    },
    table: {
      orderCode: '订单编号',
      compCode: '公司代码',
      compName: '公司名称',
      suppCode: '供应商代码',
      suppName: '供应商名称',
      plantCode: '工厂代码',
      plantName: '工厂名称',
      unloadingNo: '卸货地点',
      unloadingName: '卸货方',
      plannerNo: '采购组',
      plannerName: '采购组描述',
      customerCode: '客户编号',
      requester: '送达方要求',
      createTime: '创建时间',
      sapUpdateTime: '更新时间',
      receiveTime: '接收时间',
      isRead: '是否已阅',
      isComplete: '是否完成',
      timeBegin: '时间窗开始',
      timeEnd: '时间窗结束',
      index: '序号',
      actions: '操作',
      status: '状态',
      downloadingStatus: '下载状态',
      lastDownloadingTime: '最后下载时间'
    },
    button: {
      search: '搜索',
      reset: '重置',
      createASN: '创建ASN',
      printItemTag: '打印现品票',
      printTxt: '下载TXT文件',
      printSelectedRows: '下载选中行',
      confirmSelectedRows: '确认选中行',
      printPartsInstructionNote: '打印部品纳入指示书',
      export: '导出',
      add: '添加',
      delete: '删除',
      confirm: '确定',
      cancel: '取消',
      notification: '提示',
      warning: '警告'
    },
    divider: {
      orderItems: '订单行项目信息'
    },

    title: {
      add: '添加订单',
      update: '修改订单'
    },
    success: {
      add: '新增成功',
      update: '修改成功',
      delete: '删除成功'
    },
    confirm: {
      delete: '是否确认删除订单编号为"{orderIds}"的数据项?'
    },
    alert: {
      deleteItem: '请先选择要删除的订单行项目数据',
      selectOrder: '至少选择一条订单',
      sameClient: '所选订单需为同一客户',
      sameSupplier: '所选订单需为同一供应商',
      samePlant: '所选订单需为同一工厂',
      sameDepot: '所选订单需为同一仓库',
      sameUnloading: '所选订单需为同一卸货地点',
      orderCompleted: '无法为已完成的订单创建ASN',
      znbsNotAllowed: '禁止操作，此订单为客户直送业务订单（卸货点ZNBS）'
    },
    rules: {
      compCode: '公司代码不能为空',
      compName: '公司名称不能为空',
      plantCode: '工厂代码不能为空',
      plantName: '工厂名称不能为空',
      suppCode: '供应商代码不能为空',
      suppName: '供应商名称不能为空',
      plannerNo: '采购组不能为空',
      plannerName: '采购组描述不能为空',
      orderCode: '订单编号不能为空'
    },
    orderDetail: {
      baseInfoTitle: '订单信息',
      itemsInfoTitle: '行项目信息',
      orderCode: '订单编号',
      compCode: '公司代码',
      compName: '公司名称',
      plantCode: '工厂代码',
      plantName: '工厂名称',
      plannerNo: '采购组',
      plannerName: '采购员',
      suppCode: '供应商代码',
      suppName: '供应商名称',
      itemNo: '行号',
      articleNo: '物料号',
      articleName: '物料名称',
      quantity: '数量',
      unit: '单位',
      netPrice: '采购价格',
      priceUnit: '价格单位',
      currencyCode: '货币',
      deliveryDate: '交货日期',
      qtyPerPack: '标准包装数量',
      deliverySplit: '分割回数',
      workbinNo: '箱型',
      workbinName: '箱型描述',
      state: '状态',
      purDocType: '采购凭证类型',
      itemType: '项目类别',
      text: '采购订单头',
      orderNetWorth: '采购订单货币的订单净值',
      delIden: '采购凭证中的删除标识',
      shortText: '采购物料描述',
      oldArticleNo: '旧物料号',
      unloadingNo: '卸货地点',
      unloadingName: '卸货方',
      stockLoc: '库存地点',
      locDes: '库存地点描述',
      locAdd: '库存地点位置',
      rcvName: '收货人姓名',
      rcvTel: '收货人电话',
      inspeStrategy: '检验策略',
      zipCode: '邮政编码',
      city: '城市',
      countryCode: '国家代码',
      addTimeZone: '地址时区',
      street2: '街道2',
      street3: '街道3',
      street4: '街道4',
      title: '标题',
      content: '内容',
      customerOrderCode: '客户订单号',
      customerOrderLineCode: '客户订单行号',
      customerDeliveryDate: '客户交货日期',
      productType: '生产方式',
      rcvType: '收货方式',
      purchaseType: '自行采购类型',
      depot: '仓库'
    }
  },
  asn: {
    asnCode: 'ASN编号',
    enterAsnCode: '请输入ASN编号',
    compCode: '公司代码',
    enterCompCode: '请输入公司代码',
    compName: '公司名称',
    enterCompName: '请输入公司名称',
    suppCode: '供应商代码',
    enterSuppCode: '请输入供应商代码',
    suppName: '供应商名称',
    enterSuppName: '请输入供应商名称',
    status: '状态',
    planDeliveryDate: '预计送达日期',
    deliveryDate: '发货日期',
    startDate: '开始日期',
    endDate: '结束日期',
    search: '搜索',
    reset: '重置',
    actionsText: '操作',
    delete: '删除',
    editText: '修改',
    validation: {
      asnCodeRequired: 'ASN编号不能为空',
      docnoRequired: '文档编号不能为空',
      compCodeRequired: '公司代码不能为空',
      compNameRequired: '公司名称不能为空',
      suppCodeRequired: '供应商编号不能为空',
      suppNameRequired: '供应商名称不能为空',
      docDateRequired: '文档日期不能为空',
      dnnoRequired: 'ASN不能为空',
      planDeliveryDateRequired: '预计送达日期不能为空',
      deliveryDateRequired: '发货日期不能为空',
      quantityUsedUp: '该物料已用完，无法保存'
    },
    actions: {
      add: '添加ASN',
      editSuccess: '修改成功',
      addSuccess: '新增成功',
      confirmDelete: "是否确认删除ASN编号为'{asnCode}'的数据项?",
      deleteSuccess: '删除成功',
      confirmExport: '是否确认导出所有ASN数据项?',
      warningTitle: '警告',
      confirm: '确定',
      cancel: '取消'
    },
    upload: {
      title: 'ASN导入',
      resultTitle: '导入结果'
    },
    detail: {
      asnInfo: 'ASN信息',
      title: '标题',
      content: '内容',
      printDeliveryNote: '打印纳品书',
      printPickingList: '打印提货单',
      lineItemInfo: '行项目信息',
      dnNo: 'ASN',
      orderCode: '采购订单编号',
      plantCode: '工厂编号',
      plantName: '工厂名称',
      unloadingNo: '卸货点编号',
      unloadingName: '卸货点名称',
      sendLocNo: '工位器具发出地点编号',
      sendLocName: '工位器具发出地点名称',
      rcvLocNo: '工位器具接收地点编号',
      rcvLocName: '工位器具接收地点名称',
      asnCode: 'ASN号码',
      compCode: '公司编号',
      compName: '公司名称',
      planDeliveryDate: '预计交货日期',
      deliveryDate: '发货日期',
      suppCode: '供应商编号',
      suppName: '供应商名称'
    },
    article: {
      materialInfo: '物料信息',
      releaseNo: '下达号',
      orderLineNo: '订单行号',
      articleNo: '物料编号',
      articleName: '物料名称',
      quantity: '交货数量',
      unit: '单位',
      batchNo: '批次号',
      qtyPerPack: '每箱数量',
      packQty: '箱数',
      startWith: '条码开始',
      endWith: '条码结束',
      nonStd: '非标'
    },
    edit: {
      asnInfo: 'ASN信息',
      asnCode: 'ASN编号',
      enterAsnCode: '请输入ASN编号',
      compCode: '公司代码',
      enterCompCode: '请输入公司代码',
      compName: '公司名称',
      enterCompName: '请输入公司名称',
      suppCode: '供应商编号',
      enterSuppCode: '请输入供应商编号',
      suppName: '供应商名称',
      enterSuppName: '请输入供应商名称',
      planDeliveryDate: '预计送达日期',
      selectPlanDeliveryDate: '选择预计送达日期',
      deliveryDate: '发货日期',
      selectDeliveryDate: '选择发货日期',
      save: '保 存',
      send: '发 送',
      cancel: '取消',
      lineItemInfo: '行项目信息',
      articleInfo: '物料信息',
      addArticle: '新增',
      orderLineNo: '订单行号',
      deliveryScheduleNo: '下达号',
      articleNo: '物料编号',
      articleName: '物料名称',
      quantity: '交货数量',
      unit: '单位',
      batchNo: '批次号',
      qtyPerPack: '每箱数量',
      packQty: '箱数',
      nonStd: '非标',
      actions: '操作',
      delete: '删除',
      dnNo: 'ASN',
      orderCode: '采购订单编号',
      plantCode: '工厂编号',
      plantName: '工厂名称',
      unloadingNo: '卸货点编号',
      unloadingName: '卸货点名称',
      sendLocNo: '工位器具发出地点编号',
      sendLocName: '工位器具发出地点名称',
      rcvLocNo: '工位器具接收地点编号',
      rcvLocName: '工位器具接收地点名称',
      selectOrderItem: '选择订单行项目',
      itemNo: '行号',
      netPrice: '采购价格',
      priceUnit: '价格单位',
      currencyCode: '货币',
      confirm: '确定',
      dialog: {
        qtyPerPack: '标准包装数量',
        workbinNo: '箱型',
        workbinName: '箱型描述',
        state: '状态',
        purDocType: '采购凭证类型',
        itemType: '项目类别',
        text: '采购订单头',
        orderNetWorth: '采购订单货币的订单净值',
        delIden: '采购凭证中的删除标识',
        shortText: '采购物料描述',
        oldArticleNo: '旧物料号',
        unloadingNo: '卸货地点',
        unloadingName: '卸货方',
        stockLoc: '库存地点',
        locDes: '库存地点描述',
        locAdd: '库存地点位置',
        rcvName: '收货人姓名',
        rcvTel: '收货人电话',
        inspeStrategy: '检验策略',
        zipCode: '邮政编码',
        city: '城市',
        countryCode: '国家代码',
        addTimeZone: '地址时区',
        street2: '街道2',
        street3: '街道3',
        street4: '街道4'
      },
      validation: {
        planDeliveryDateRequired: '预计送达日期不能为空',
        deliveryDateRequired: '发货日期不能为空',
        batchNoRequired: '批次号不能为空',
        quantityRequired: '交货数量不能为空',
        qtyPerPackRequired: '每箱数量不能为空',
        quantityUsedUp: '该物料已用完，无法保存',
        affectedOrders: '受影响的订单'
      },
      confirmCancel: '如有未保存的修改将会丢失，是否确认取消?',
      warning: '警告',
      atLeastOneLineItem: '至少有一条行项目',
      atLeastOneArticle: '至少有一条物料信息',
      maxPackQtyExceeded: '总箱数不能超过{max}',
      sendSuccess: '发送成功',
      createSuccess: '创建成功',
      saveSuccess: '保存成功'
    },
    button: {
      printPickList: '打印提货单',
      printNpsSls: '打印纳品书-受领书',
      printProdTag: '打印现品票'
    },
    alert: {
      selectOnePrint: '请选择需要打印的ASN',
      asnDraft: 'ASN未发送，不允许打印'
    }
  },
  feedback: {
    dnNo: 'ASN编号/收货标识',
    enterDnNo: '请输入SN',
    compCode: '公司代码',
    enterCompCode: '请输入公司代码',
    compName: '公司名称',
    enterCompName: '请输入公司名称',
    status: '状态',
    invoiceTax: '增值税总额',
    enterInvoiceTax: '请输入增值税总额',
    invoiceNo: '金税发票号',
    enterInvoiceNo: '请输入金税发票号',
    invoiceDate: '开票日期',
    enterInvoiceDate: '请输入开票日期',
    totalAmount: '开票金额',
    currency: '币种',
    receivingDate: '接收日期',
    deliveryNoteDate: '发送日期',
    downloadStatus: '下载状态',
    delFlag: '删除标志',
    suppCode: '供应商代码',
    enterSuppCode: '请输入供应商代码',
    suppName: '供应商名称',
    enterSuppName: '请输入供应商名称',
    plantCode: '工厂代码',
    enterPlantCode: '请输入工厂代码',
    plantName: '工厂名称',
    enterPlantName: '请输入工厂名称',
    search: '搜索',
    reset: '重置',
    export: '导出',
    confirm: '确定',
    cancel: '取消',
    actionsText: '操作',
    viewText: '查看ASN',
    addFeedback: '添加收货反馈',
    updateSuccess: '修改成功',
    addSuccess: '新增成功',
    confirmDelete: "是否确认删除ID为'{feedId}'的收货反馈?",
    warning: '警告',
    deleteSuccess: '删除成功',
    confirmExport: '是否确认导出所有收货反馈数据?',
    asnCodeNotFound: 'ASN编号不匹配',
    updateInvoiceInfo: '编辑结算单开票信息',
    alert: {
      deleteItem: '请先选择要删除的行项目数据',
      selectAtLeastOne: '至少选择一行项目',
      sameClient: '所选行项目需为同一客户',
      inValidInvoiceInfo: '发票信息有误，请检查后重新提交。'
    },
    validation: {
      dnNoRequired: 'ASN不能为空',
      compCodeRequired: '公司代码不能为空',
      compNameRequired: '公司名称不能为空',
      plantCodeRequired: '工厂代码不能为空',
      plantNameRequired: '工厂名称不能为空',
      suppCodeRequired: '供应商代码不能为空',
      suppNameRequired: '供应商名称不能为空'
    },
    detail: {
      receiptFeedbackInfo: '收货反馈信息',
      title: '标题',
      content: '内容',
      lineItemInfo: '行项目信息',
      orderCode: '采购订单号',
      orderLineNo: '采购订单行号',
      articleNo: '物料编码',
      articleName: '物料名称',
      rcvDate: '收货日期',
      rcvTime: '时间',
      quantity: '交货数量',
      unit: '单位',
      rcvDocNo: '收货凭证号码',
      articleDocAnnual: '物料凭证年度',
      rcvDocItemNo: '收货凭证项目号',
      dnNo: 'ASN',
      suppCode: '供应商编号',
      suppName: '供应商名称',
      plantCode: '工厂编号',
      plantName: '工厂名称',
      compCode: '公司编号',
      compName: '公司名称'
    },
    button: {
      downloadExcel: '下载结算单',
      confirmFeedback: '确认结算单',
      edit: '编辑',
      save: '保存',
      cancel: '取消'
    }
  },
  forecast: {
    form: {
      forecastCode: '预测编号',
      inputForecastCode: '请输入预测编号',
      version: '预测版本号',
      inputVersion: '请输入预测版本号',
      compCode: '公司代码',
      inputCompCode: '请输入公司代码',
      compName: '公司名称',
      inputCompName: '请输入公司名称',
      suppCode: '供应商代码',
      inputSuppCode: '请输入供应商代码',
      suppName: '供应商名称',
      inputSuppName: '请输入供应商名称',
      plantCode: '工厂代码',
      inputPlantCode: '请输入工厂代码',
      plantName: '工厂名称',
      inputPlantName: '请输入工厂名称',
      search: '搜索',
      reset: '重置',
      export: '导出',
      downloadExcel: '下载预测Excel',
      forecastConfirm: '预测确认'
    },
    table: {
      forecastCode: '预测编号',
      compCode: '公司代码',
      compName: '公司名称',
      suppCode: '供应商代码',
      suppName: '供应商名称',
      plantCode: '工厂代码',
      plantName: '工厂名称',
      status: '状态',
      downloadStatus: '下载状态',
      lastDownloadTime: '最后下载时间'
    },
    alert: {
      selectAtLeastOne: '至少选择一行项目',
      notAllNew: '预测号 {notNewCode} 的状态不是New，无法确认'
    },
    dialog: {
      title: '添加或修改预测',
      titleAdd: '添加预测',
      titleEdit: '修改预测',
      forecastCode: '预测编号',
      inputForecastCode: '请输入预测编号',
      version: '预测版本号',
      inputVersion: '请输入预测版本号',
      compCode: '公司代码',
      inputCompCode: '请输入公司代码',
      compName: '公司名称',
      enterCompName: '请输入公司名称',
      plantCode: '工厂代码',
      inputPlantCode: '请输入工厂代码',
      plantName: '工厂名称',
      inputPlantName: '请输入工厂名称',
      suppCode: '供应商代码',
      inputSuppCode: '请输入供应商代码',
      suppName: '供应商名称',
      inputSuppName: '请输入供应商名称',
      confirm: '确 定',
      cancel: '取 消'
    },
    messages: {
      updateSuccess: '修改成功',
      addSuccess: '新增成功',
      deleteSuccess: '删除成功',
      deleteConfirm: '是否确认删除预测编号为 "{id}" 的数据项?',
      exportConfirm: '是否确认导出所有预测数据项?'
    },

    detail: {
      forecastInfo: '预测信息',
      lineItemInfo: '行项目信息',
      title: '标题',
      content: '内容',
      forecastCode: '预测编号',
      compCode: '公司编号',
      compName: '公司名称',
      plantCode: '工厂编号',
      plantName: '工厂名称',
      version: '预测版本号',
      suppCode: '供应商编号',
      suppName: '供应商名称',
      articleNo: '物料编码',
      articleName: '物料名称',
      deliveryDate: '交货日期',
      quantity: '交货数量',
      unit: '单位',
      durType: '期间类型',
      proType: '计划类型',
      poddet: '采购计划协议号'
    }
  },
  inventory: {
    form: {
      compCode: '公司代码',
      inputCompCode: '请输入公司代码',
      compName: '公司名称',
      enterCompName: '请输入公司名称',
      suppCode: '供应商代码',
      inputSuppCode: '请输入供应商代码',
      suppName: '供应商名称',
      inputSuppName: '请输入供应商名称',
      plantCode: '工厂代码',
      inputPlantCode: '请输入工厂代码',
      plantName: '工厂名称',
      inputPlantName: '请输入工厂名称',
      updateDate: '更新日期',
      updateTime: '更新时间',
      startDate: '开始日期',
      endDate: '结束日期',
      startTime: '开始时间',
      endTime: '结束时间',
      search: '搜索',
      reset: '重置',
      export: '导出'
    },
    table: {
      compCode: '公司代码',
      compName: '公司名称',
      suppCode: '供应商代码',
      suppName: '供应商名称',
      plantCode: '工厂代码',
      plantName: '工厂名称',
      updateDate: '更新日期',
      updateTime: '更新时间',
      actions: '操作',
      view: '查看'
    },
    dialog: {
      compCode: '公司代码',
      inputCompCode: '请输入公司代码',
      compName: '公司名称',
      enterCompName: '请输入公司名称',
      plantCode: '工厂代码',
      inputPlantCode: '请输入工厂代码',
      plantName: '工厂名称',
      inputPlantName: '请输入工厂名称',
      suppCode: '供应商代码',
      inputSuppCode: '请输入供应商代码',
      suppName: '供应商名称',
      inputSuppName: '请输入供应商名称',
      updateDate: '更新日期',
      selectUpdateDate: '选择更新日期',
      confirm: '确 定',
      cancel: '取 消',
      addTitle: '添加寄售库存'
    },
    validation: {
      compCodeRequired: '公司代码不能为空',
      compNameRequired: '公司名称不能为空',
      plantCodeRequired: '工厂不能为空',
      plantNameRequired: '工厂名称不能为空',
      suppCodeRequired: '供应商不能为空',
      suppNameRequired: '供应商名称不能为空',
      updateDateRequired: '更新日期不能为空',
      updateTimeRequired: '更新时间不能为空'
    },
    messages: {
      updateSuccess: '修改成功',
      addSuccess: '新增成功',
      deleteConfirm: '是否确认删除寄售库存编号为 "{id}" 的数据项?',
      deleteSuccess: '删除成功',
      exportConfirm: '是否确认导出所有寄售库存数据项?'
    },
    detail: {
      consignmentInfo: '寄售库存信息',
      lineItemInfo: '行项目信息',
      title: '标题',
      content: '内容',
      compCode: '公司编号',
      compName: '公司名称',
      plantCode: '工厂编号',
      plantName: '工厂名称',
      suppCode: '供应商编号',
      suppName: '供应商名称',
      updateDate: '更新日期',
      updateTime: '更新时间',
      articleNo: '物料编码',
      articleName: '物料名称',
      quantity: '数量',
      unit: '单位',
      days: '天数',
      remark: '备注'
    }
  },
  consignment: {
    form: {
      compCode: '公司代码',
      inputCompCode: '请输入公司代码',
      compName: '公司名称',
      enterCompName: '请输入公司名称',
      suppCode: '供应商代码',
      inputSuppCode: '请输入供应商代码',
      suppName: '供应商名称',
      inputSuppName: '请输入供应商名称',
      plantCode: '工厂代码',
      inputPlantCode: '请输入工厂代码',
      plantName: '工厂名称',
      inputPlantName: '请输入工厂名称',
      updateDate: '更新日期',
      updateTime: '更新时间',
      startDate: '开始日期',
      endDate: '结束日期',
      startTime: '开始时间',
      endTime: '结束时间',
      search: '搜索',
      reset: '重置',
      export: '导出'
    },
    table: {
      compCode: '公司代码',
      compName: '公司名称',
      suppCode: '供应商代码',
      suppName: '供应商名称',
      plantCode: '工厂代码',
      plantName: '工厂名称',
      updateDate: '更新日期',
      updateTime: '更新时间',
      actions: '操作',
      view: '查看'
    },
    dialog: {
      compCode: '公司代码',
      inputCompCode: '请输入公司代码',
      compName: '公司名称',
      enterCompName: '请输入公司名称',
      plantCode: '工厂代码',
      inputPlantCode: '请输入工厂代码',
      plantName: '工厂名称',
      inputPlantName: '请输入工厂名称',
      suppCode: '供应商代码',
      inputSuppCode: '请输入供应商代码',
      suppName: '供应商名称',
      inputSuppName: '请输入供应商名称',
      updateDate: '更新日期',
      selectUpdateDate: '选择更新日期',
      confirm: '确定',
      cancel: '取消',
      addTitle: '添加寄售库存'
    },
    validation: {
      compCodeRequired: '公司代码不能为空',
      compNameRequired: '公司名称不能为空',
      plantCodeRequired: '工厂代码不能为空',
      plantNameRequired: '工厂名称不能为空',
      suppCodeRequired: '供应商代码不能为空',
      suppNameRequired: '供应商名称不能为空',
      updateDateRequired: '更新日期不能为空',
      updateTimeRequired: '更新时间不能为空'
    },
    messages: {
      updateSuccess: '修改成功',
      addSuccess: '新增成功',
      deleteConfirm: '是否确认删除寄售库存编号为 "{id}" 的数据项?',
      deleteSuccess: '删除成功',
      exportConfirm: '是否确认导出所有寄售库存数据项?'
    },
    detail: {
      consignmentInfo: '寄售库存信息',
      lineItemInfo: '行项目信息',
      title: '标题',
      content: '内容',
      compCode: '公司编号',
      compName: '公司名称',
      plantCode: '工厂编号',
      plantName: '工厂名称',
      suppCode: '供应商编号',
      suppName: '供应商名称',
      updateDate: '更新日期',
      updateTime: '更新时间',
      articleNo: '物料编码',
      articleName: '物料名称',
      quantity: '数量',
      unit: '单位',
      days: '天数',
      remark: '备注'
    }
  },
  system: {
    common: {
      search: '搜索',
      reset: '重置',
      add: '新增',
      edit: '编辑',
      delete: '删除',
      export: '导出',
      actions: '操作',
      remark: '备注',
      inputContent: '请输入内容',
      confirm: '确定',
      cancel: '取消',
      loading: '加载中，请稍候',
      expandCollapse: '展开/折叠',
      selectAllNone: '全选/全不选',
      parentChildLinkage: '父子联动',
      rangeSeparator: '至',
      startDate: '开始日期',
      endDate: '结束日期',
      createTime: '创建时间',
      sapUpdateTime: 'SAP更新时间',
      receiveTime: '接收时间',
      creationTime: '创建时间',
      warning: '警告',
      prompt: '提示',
      success: '成功',
      createSuccess: '创建成功',
      updateSuccess: '更新成功',
      deleteSuccess: '删除成功',
      id: '编号'
    },
    user: {
      deptPlaceholder: '请输入供应商名称',
      usernameLabel: '用户名称',
      usernamePlaceholder: '请输入用户名称',
      phoneLabel: '手机号码',
      phonePlaceholder: '请输入手机号码',
      statusLabel: '状态',
      statusPlaceholder: '用户状态',
      creationTimeLabel: '创建时间',
      dateRangeSeparator: '-',
      startDatePlaceholder: '开始日期',
      endDatePlaceholder: '结束日期',
      searchButton: '搜索',
      resetButton: '重置',
      addButton: '新增',
      editButton: '修改',
      deleteButton: '删除',
      importButton: '导入',
      exportButton: '导出',
      userId: '用户编号',
      userName: '用户名称',
      nickName: '用户昵称',
      department: '供应商',
      carrier: '承运商',
      phone: '手机号码',
      status: '状态',
      creationTime: '创建时间',
      actions: '操作',
      resetPasswordButton: '重置密码',
      nickNameLabel: '用户昵称',
      nickNamePlaceholder: '请输入用户昵称',
      departmentLabel: '归属供应商',
      departmentPlaceholder: '请选择归属供应商',
      emailLabel: '邮箱',
      emailPlaceholder: '请输入邮箱',
      passwordLabel: '用户密码',
      passwordPlaceholder: '请输入用户密码',
      genderLabel: '用户性别',
      genderPlaceholder: '请选择',
      positionLabel: '承运商',
      positionPlaceholder: '请选择承运商',
      roleLabel: '角色',
      rolePlaceholder: '请选择',
      remarkLabel: '备注',
      remarkPlaceholder: '请输入内容',
      dragUpload: '将文件拖到此处，或',
      clickUpload: '点击上传',
      updateExistingUsers: '是否更新已存在的用户数据',
      downloadTemplate: '下载模板',
      uploadTip: '提示：仅允许导入“xls”或“xlsx”格式文件！',
      enable: '启用',
      disable: '停用',
      confirmChangeStatus: '确认要"{text}""{user}"用户吗?',
      statusSuccess: '{text}成功',
      addUser: '添加用户',
      editUser: '修改用户',
      resetPasswordPrompt: '请输入"{user}"的新密码',
      resetPasswordSuccess: '修改成功，新密码是：{password}',
      editSuccess: '用户更新成功',
      addSuccess: '用户新增成功',
      deleteConfirm: '是否确认删除用户"{user}"?',
      deleteSuccess: '用户删除成功',
      exportConfirm: '是否确认导出所有用户数据项?',
      importUser: '用户导入',
      importResult: '导入结果',
      validation: {
        usernameRequired: '用户名称不能为空',
        nickNameRequired: '用户昵称不能为空',
        passwordRequired: '用户密码不能为空',
        emailInvalid: '请输入正确的邮箱地址',
        phoneInvalid: '请输入正确的手机号码'
      },
      profile: {
        personalInfo: '个人信息',
        userName: '用户名称',
        phone: '手机号码',
        email: '用户邮箱',
        department: '所属供应商',
        role: '所属角色',
        creationDate: '创建日期',
        basicInfo: '基本资料',
        basicInfoTab: '基本资料',
        changePasswordTab: '修改密码',
        resetPwd: {
          title: '修改密码',
          oldPassword: '旧密码',
          oldPasswordPlaceholder: '请输入旧密码',
          newPassword: '新密码',
          newPasswordPlaceholder: '请输入新密码',
          confirmPassword: '确认密码',
          confirmPasswordPlaceholder: '请再次输入新密码',
          saveButton: '保存',
          closeButton: '关闭',
          oldPasswordRequired: '请输入旧密码',
          newPasswordRequired: '请输入新密码',
          passwordMinLength: '密码长度不能少于8个字符',
          complexityRequirement: '密码需包含大写字母、小写字母、数字、特殊字符中的至少三种',
          noConsecutiveChars: '密码不能包含连续三个及以上相同字符',
          confirmPasswordRequired: '请再次输入新密码',
          passwordMismatch: '两次输入的密码不一致',
          updateSuccess: '修改成功'
        },
        userAvatar: {
          clickToUpload: '点击上传头像',
          dialogTitle: '修改头像',
          selectButton: '选择',
          submitButton: '提交',
          fileError: '文件格式错误，请上传图片类型,如：JPG，PNG后缀的文件。',
          uploadSuccess: '修改成功'
        },
        userinfo: {
          nickName: '用户昵称',
          phoneNumber: '手机号码',
          email: '邮箱',
          gender: '性别',
          male: '男',
          female: '女',
          save: '保存',
          close: '关闭',
          nickNameRequired: '用户昵称不能为空',
          emailRequired: '邮箱地址不能为空',
          invalidEmail: '请输入正确的邮箱地址',
          phoneRequired: '手机号码不能为空',
          invalidPhone: '请输入正确的手机号码',
          success: '修改成功'
        }
      }
    },
    role: {
      roleName: '角色名称',
      enterRoleName: '请输入角色名称',
      roleKey: '权限字符',
      enterRoleKey: '请输入权限字符',
      status: '状态',
      roleStatus: '角色状态',
      createTime: '创建时间',
      sapUpdateTime: 'SAP更新时间',
      receiveTime: '接收时间',
      rangeSeparator: '-',
      startDate: '开始日期',
      endDate: '结束日期',
      search: '搜索',
      reset: '重置',
      add: '新增',
      update: '修改',
      delete: '删除',
      export: '导出',
      roleId: '角色编号',
      roleSort: '显示顺序',
      actions: '操作',
      dataScope: '数据权限',
      menuPermission: '菜单权限',
      expandCollapse: '展开/折叠',
      selectAllNone: '全选/全不选',
      linkage: '父子联动',
      loading: '加载中，请稍后',
      remark: '备注',
      enterContent: '请输入内容',
      confirm: '确定',
      cancel: '取消',
      dataPermissions: '权限范围',
      allDataPermissions: '全部数据权限',
      customDataPermissions: '自定数据权限',
      departmentDataPermissions: '本供应商数据权限',
      departmentAndSubDataPermissions: '本供应商及以下数据权限',
      personalDataPermissions: '仅本人数据权限',
      roleNameRequired: '角色名称不能为空',
      roleKeyRequired: '权限字符不能为空',
      roleSortRequired: '角色顺序不能为空',
      enable: '启用',
      disable: '停用',
      confirmStatusChange: '确认要 "{text}" "{roleName}" 角色吗?',
      warning: '警告',
      statusChangeSuccess: '{text} 成功',
      addRole: '添加角色',
      updateRole: '修改角色',
      assignDataPermissions: '分配数据权限',
      updateSuccess: '修改成功',
      addSuccess: '新增成功',
      confirmDelete: '是否确认删除角色编号为 "{roleIds}" 的数据项?',
      deleteSuccess: '删除成功',
      confirmExport: '是否确认导出所有角色数据项?'
    },
    supplier: {
      supplierCode: '供应商代码',
      enterSupplierCode: '请输入供应商代码',
      supplierName: '供应商名称',
      enterSupplierName: '请输入供应商名称',
      orderNum: '排序',
      leader: '负责人',
      enterLeader: '请输入负责人',
      phone: '联系电话',
      enterPhone: '请输入联系电话',
      email: '邮箱',
      enterEmail: '请输入邮箱',
      status: '状态',
      selectStatus: '请选择供应商状态',
      parentSupplier: '上级供应商',
      selectParentSupplier: '请选择上级供应商',
      addSupplier: '添加供应商',
      editSupplier: '修改供应商',
      addSuccess: '新增成功',
      editSuccess: '修改成功',
      deleteSuccess: '删除成功',
      confirmDelete: '是否确认删除名称为 "{name}" 的供应商?',
      parentSupplierRequired: '上级供应商不能为空',
      supplierCodeRequired: '供应商代码不能为空',
      supplierNameRequired: '供应商名称不能为空',
      orderNumRequired: '排序不能为空',
      invalidEmail: '请输入正确的邮箱地址',
      invalidPhone: '请输入正确的手机号码'
    },
    carrier: {
      title: '承运商管理',
      code: '承运商编码',
      name: '承运商名称',
      sort: '显示顺序',
      status: '状态',
      createTime: '创建时间',
      sapUpdateTime: 'SAP更新时间',
      receiveTime: '接收时间',
      remark: '备注',
      id: '承运商ID',
      placeholder: {
        code: '请输入承运商编码',
        name: '请输入承运商名称',
        status: '请选择状态',
        sort: '请输入显示顺序',
        remark: '请输入备注'
      },
      add: '添加承运商',
      edit: '修改承运商',
      delete: '是否确认删除承运商编号为"{postIds}"的数据项?',
      export: '是否确认导出所有承运商数据项?',
      validation: {
        nameRequired: '承运商名称不能为空',
        codeRequired: '承运商编码不能为空',
        sortRequired: '显示顺序不能为空'
      }
    }
  },
  common: {
    confirm: '确定',
    cancel: '取消',
    warning: '警告',
    prompt: '提示',
    success: '成功',
    noData: '暂无数据'
  },
  errorPage404: {
    error: '404错误!',
    message: '找不到网页！',
    info: '对不起，您正在寻找的页面不存在。尝试检查URL的错误，然后按浏览器上的刷新按钮或尝试在我们的应用程序中找到其他内容。',
    goHome: '返回首页',
    notFound: '找不到网页！'
  },
  errorPage: {
    back: '返回',
    error401: '401错误!',
    noPermission: '您没有访问权限！',
    apology: '对不起，您没有访问权限，请不要进行非法操作！您可以返回主页面',
    goHome: '回首页',
    imageAlt: '女孩的冰淇淋掉了。'
  },
  setting: {
    themeSettings: '主题风格设置',
    themeColor: '主题颜色',
    systemLayout: '系统布局配置',
    enableTopNav: '开启 TopNav',
    enableTagsView: '开启 Tags-Views',
    fixedHeader: '固定 Header',
    showLogo: '显示 Logo',
    saveConfig: '保存配置',
    resetConfig: '重置配置',
    saving: '正在保存到本地，请稍后...',
    clearing: '正在清除设置缓存并刷新，请稍后...'
  },
  rightToolbar: {
    showSearch: '显示搜索',
    hideSearch: '隐藏搜索',
    refresh: '刷新',
    showHideColumns: '显隐列',
    showHide: '显示/隐藏',
    show: '显示',
    hide: '隐藏'
  },
  navbar: {
    dashboard: '首页',
    github: '项目地址',
    logOut: '退出登录',
    profile: '个人中心',
    theme: '换肤',
    size: '布局大小'
  },
  login: {
    title: 'DataLink管理系统登录',
    username: {
      placeholder: '用户名',
      empty: '用户名不能为空'
    },
    password: {
      placeholder: '密码',
      empty: '密码不能为空'
    },
    verificationCode: {
      placeholder: '验证码',
      empty: '验证码不能为空'
    },
    rememberPwd: '记住密码',
    logIn: '登 录',
    loggingIn: '登 录 中...'
  },
  settings: {
    title: '系统布局配置',
    theme: '主题色',
    tagsView: '开启 Tags-View',
    fixedHeader: '固定 Header',
    sidebarLogo: '侧边栏 Logo'
  },
  tagsView: {
    refresh: '刷新',
    close: '关闭',
    closeOthers: '关闭其它',
    closeAll: '关闭所有'
  },
  notification: {
    logout: {
      title: '提示',
      message: '确定注销并退出系统吗？'
    },
    ok: '确定',
    cancel: '取消'
  },
  language: {
    message: '语言切换成功'
  },
  utils: {
    request: {
      sessionExpired: '登录状态已过期，您可以继续留在该页面，或者重新登录',
      systemPrompt: '系统提示',
      relogin: '重新登录',
      cancel: '取消',
      networkError: '后端接口连接异常',
      requestTimeout: '系统接口请求超时',
      requestError: '系统接口 {statusCode} 异常'
    }
  },
  supplyPlan: {
    plan: {
      title: '支给计划',
      tableTitle: '支给计划表',
      lastMonthRemaining: '上月支给剩余',
      monthlyTotal: '月合计',
      week1: '第1周',
      week2: '第2周',
      week3: '第3周',
      week4: '第4周',
      week5: '第5周',
      week6: '第6周',
      week7: '第7周',
      planAmount: '计划数',
      actualAmount: '实际数',
      days: {
        mon: '周一',
        tue: '周二',
        wed: '周三',
        thu: '周四',
        fri: '周五',
        sat: '周六',
        sun: '周日'
      }
    },
    form: {
      yearMonth: '年月',
      client: '客户',
      clientPlaceholder: '请输入客户名称',
      depot: '仓库',
      depotPlaceholder: '请输入仓库名称',
      factory: '需求工厂',
      factoryPlaceholder: '请输入需求工厂',
      partNumber: '部件编号',
      partNumberPlaceholder: '请输入部件编号'
    },
    factories: {
      factory1: '工厂1',
      factory2: '工厂2',
      factory3: '工厂3'
    },
    buttons: {
      search: '搜索',
      reset: '重置'
    },
    info: {
      basicInfo: '基本信息',
      client: '客户',
      depot: '仓库',
      factory: '需求工厂',
      partNumber: '部件编号',
      requirement: '需求量',
      nMonth: '当前月',
      n1Month: '下月',
      n2Month: '下下月',
      monthly: '月度',
      daily: '每日',
      deliveryMethod: '交货方式',
      supplyType: '供应类型',
      supplyMethod: '供应方式',
      deliveryLocation: '交货地点',
      basicUnit: '基本数量单位',
      orderUnit: '订货单位',
      allocationLot: '分配批量',
      snep: 'SNEP',
      currentYearMonth: '当前年月',
      clientInventory: '客户库存',
      confirmedDate: '确认日期',
      updatedDateTime: '更新时间 - 时分'
    }
  },
  internalForecast: {
    title: '计划内示下载',
    buttons: {
      downloadWeekly: '下载周内示',
      downloadThreeMonths: '下载三个月内示',
      downloadYearly: '下载年度内示',
      downloadWeeklyTxt: '下载周内示txt文件',
      downloadThreeMonthsTxt: '下载三个月内示txt文件',
      downloadYearlyTxt: '下载年内示txt文件'
    },
    badge: {
      undownloaded: '未下载'
    }
  },

  loadProposalVehicle: {
    function: '功能',
    functionSearch: '照会',
    functionStatusChange: '状态変更',
    functionShipment: '配车注册',
    status: '状态',
    statusAll: '全て',
    statusDelivered: '配完',
    statusConfirmed: '配確',
    materialFactor: '料率区分',
    materialP: 'P',
    materialQ: 'Q',
    materialF: 'F',
    transportCompany: '运输公司',
    client: '客户',
    pickupDate: '提货日期-时间',
    deliveryDate: '交付日期-时间',
    depot: '仓库',
    factory: '工厂',
    deliveryLocation: '交付地点',
    ticketNo: '货运单号',
    search: '搜索',
    reload: '重新加载',
    clear: '清除',
    addRow: '新增行',
    deleteSelected: '删除选中',
    to: '至',
    startDate: '开始日期',
    endDate: '结束日期',
    addRowWarning: '请点击确认按钮，保存数据！',
    placeholder: {
      depot: '请输入仓库',
      factory: '请输入工厂',
      deliveryLocation: '请输入交付地点',
      ticketNo: '请输入货运单号',
      materialFactorPlaceholder: '请输入料率区分'
    }
  },
  loadProposalVehicleTable: {
    transportPlanId: '编号',
    status: '状态',
    statusDraft: '草稿',
    statusConfirmed: '荷确',
    statusInTransit: '配中',
    statusAssigned: '配确',
    statusCompleted: '配完',
    statusDelivered: '纳完',
    materialFactor: '料率区分',
    way: '方式',
    pickupDateTime: '提货日期和时间',
    pickupDate: '提货日期',
    pickupTime: '提货时间',
    clientCode: '客户代码',
    depot: '仓库',
    deliveryLocation: '交付地点',
    factory: '工厂',
    deliveryDateTime: '交付日期和时间',
    deliveryDate: '交付日期',
    deliveryTime: '交付时间',
    port: '出发港口',
    weight: '货物重量',
    weightTotal: '总重量',
    palletQuantity: '托盘数量',
    totalQuantity: '总数量',
    average: '平均值',
    ticketNo: '货运单号',
    transportPlan: '运输计划',
    company: '运输公司',
    pickupTimeShort: '提货时分',
    pickupQuantity: '提货数量',
    carType: '车辆类型',
    carNo: '车牌号',
    driver: '司机',
    deliveryLocationName: '交货地点名称',
    companyName: '公司名称',
    recordMaintenance: '记录维护信息',
    registrationInfo: '登记信息',
    createDate: '创建日期',
    createTime: '创建时间',
    createBy: '创建用户',
    updateInfo: '更新信息',
    updateDate: '更新日期',
    updateTime: '更新时间',
    updateBy: '更新用户',
    actions: '操作',
    confirm: '确认',
    smallCar: '小型车',
    mediumCar: '中型车',
    largeCar: '大型车',
    placeholder: {
      depot: '请输入仓库',
      factory: '请输入工厂',
      deliveryLocation: '请输入交付地点',
      ticketNo: '请输入货运单号',
      materialFactorPlaceholder: '请输入料率区分'

    }
  },
  kanban: {
    form: {
      demandCode: '需求号',
      issueNo: '发行编号',
      deliveryInstructionDate: '交付指示日期',
      deliveryInstructionTime: '交付指示时间',
      deliveryInstructionYy: '交付指示日期时间',
      maker: '制造商',
      depot: '仓库',
      customerPartsNo: '零件编号',
      deliveryTicketNo: '交付单号'
    },
    placeholder: {
      demandCode: '请输入需求号',
      issueNo: '请输入发行编号',
      deliveryInstructionDate: '请选择交付指示日期',
      deliveryInstructionTime: '请选择交付指示时间',
      deliveryInstructionYy: '请输入交付指示日期时间',
      maker: '请输入制造商',
      depot: '请输入仓库',
      customerPartsNo: '请输入零件编号',
      deliveryTicketNo: '请输入交付单号'
    },
    table: {
      demandCode: '需求号',
      issueNo: '发行编号',
      deliveryInstructionDate: '交付指示日期',
      deliveryInstructionTime: '交付指示时间',
      deliveryInstructionYy: '交付指示日期时间',
      maker: '制造商',
      depot: '仓库',
      customerPartsNo: '零件编号',
      deliveryTicketNo: '交付单号'
    },
    button: {
      search: '搜索',
      reset: '重置',
      printSvSpec: '打印交付指示(SV规格)',
      printLineKdSpec: '打印交付指示(生产线KD规格)',
      printLineSpec: '打印交付指示(生产线规格)'
    },
    alert: {
      selectKanban: '请至少选择一条看板数据'
    }
  }
}
