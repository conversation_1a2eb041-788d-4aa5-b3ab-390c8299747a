<template>
  <div class="app-container">
    <el-form
      v-show="showSearch"
      ref="queryForm"
      :model="queryParams"
      :inline="true"
    >
      <el-form-item :label="$t('feedback.dnNo')" prop="dnNo">
        <el-input
          v-model="queryParams.dnNo"
          :placeholder="$t('feedback.enterDnNo')"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item v-if="!isCarrierWithDepot" :label="$t('order.form.depot')" prop="depot">
        <el-input
          v-model="queryParams.depot"
          :placeholder="$t('order.placeholder.depot')"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item :label="$t('feedback.compCode')" prop="compCode">
        <el-input
          v-model="queryParams.compCode"
          :placeholder="$t('feedback.enterCompCode')"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item :label="$t('feedback.suppCode')" prop="suppCode">
        <el-input
          v-model="queryParams.suppCode"
          :placeholder="$t('feedback.enterSuppCode')"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <!-- <el-form-item :label="$t('feedback.suppName')" prop="suppName">
        <el-input
          v-model="queryParams.suppName"
          :placeholder="$t('feedback.enterSuppName')"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item> -->
      <el-form-item :label="$t('feedback.plantCode')" prop="plantCode">
        <el-input
          v-model="queryParams.plantCode"
          :placeholder="$t('feedback.enterPlantCode')"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <!-- <el-form-item :label="$t('feedback.plantName')" prop="plantName">
        <el-input
          v-model="queryParams.plantName"
          :placeholder="$t('feedback.enterPlantName')"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item> -->
      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
        >{{ $t("feedback.search") }}</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">{{
          $t("feedback.reset")
        }}</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <!-- <el-col :span="1.5">
        <el-button
          type="warning"
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['datamanage:feedback:export']"
        >{{ $t('feedback.export') }}</el-button>
      </el-col>

      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-printer"
          size="mini"
          :loading="printTxtLoading"
          @click="handlePrintTxt"
        >{{
          $t('order.button.printTxt') }}</el-button>
      </el-col>
       -->

      <el-col :span="1.5">
        <el-button
          type="primary"
          icon="el-icon-download"
          size="mini"
          :loading="excelDownloading"
          @click="handleExcelDownload"
        >{{
          $t('feedback.button.downloadExcel') }}</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="primary"
          size="mini"
          :loading="confirmLoading"
          @click="feedbackConfirm"
        >{{ $t('feedback.button.confirmFeedback') }}
        </el-button>
      </el-col>
      <right-toolbar
        :show-search.sync="showSearch"
        @queryTable="getList"
      />
    </el-row>

    <el-table
      ref="invoiceTableRef"
      v-loading="loading"
      :data="feedbackList"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column :label="$t('feedback.dnNo')" align="center" prop="dnNo">
        <template slot-scope="scope">
          <router-link
            :to="{
              name: 'FeedbackDetail',
              params: { feedbackId: scope.row.feedId },
            }"
            class="link-type"
          >
            <span>{{ scope.row.dnNo }}</span>
          </router-link>
        </template>
      </el-table-column>
      <el-table-column :label="$t('feedback.suppCode')" align="center" prop="suppCode" />
      <el-table-column :label="$t('feedback.compCode')" align="center" prop="compCode" />
      <el-table-column :label="$t('feedback.status')" align="center" prop="status" />
      <el-table-column :label="$t('feedback.invoiceTax')" align="center" prop="invoiceTax" />
      <el-table-column :label="$t('feedback.invoiceNo')" align="center" prop="invoiceNo" />
      <el-table-column :label="$t('feedback.invoiceDate')" align="center" prop="invoiceDate">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.invoiceDate, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('feedback.totalAmount')" align="center" prop="totalAmount" />
      <el-table-column :label="$t('feedback.currency')" align="center" prop="currency" />
      <el-table-column :label="$t('feedback.receivingDate')" align="center" prop="receivingDate" />
      <el-table-column :label="$t('feedback.deliveryNoteDate')" align="center" prop="deliveryNoteDate" />
      <el-table-column :label="$t('feedback.downloadStatus')" align="center" prop="downloadStatus" />
      <el-table-column :label="$t('feedback.delFlag')" align="center" prop="delFlag" />

      <el-table-column
        :label="$t('feedback.actionsText')"
        align="center"
        class-name="small-padding fixed-width"
      >
        <template v-if="scope.row.status === 'New'" slot-scope="scope">
          <el-button
            v-if="scope.row.dnNo && scope.row.dnNo.startsWith('ASN')"
            size="mini"
            type="text"
            icon="el-icon-view"
            @click="handleView(scope.row)"
          >{{ $t("feedback.viewText") }}</el-button>
          <el-button type="text" size="mini" @click="editRow(scope.row.feedId)">{{ $t('feedback.button.edit') }}</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改收货反馈对话框 -->
    <el-dialog :title="$t('feedback.updateInvoiceInfo')" :visible.sync="open" width="500px" append-to-body @close="cancelEdit">
      <el-form :model="editCache" label-width="100px">
        <el-row>
          <el-col :span="24">
            <el-form-item :label="$t('feedback.invoiceTax')" prop="supplierCode">
              <el-input-number v-model="editCache.invoiceTax" :controls="false" class="w-full" />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item :label="$t('feedback.invoiceNo')" prop="supplierCode">
              <el-input v-model="editCache.invoiceNo" :placeholder="$t('feedback.enterInvoiceNo')" />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item :label="$t('feedback.invoiceDate')" prop="supplierCode">
              <el-date-picker
                v-model="editCache.invoiceDate"
                type="date"
                value-format="yyyy-MM-dd"
                :placeholder="$t('feedback.enterInvoiceDate')"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="saveEdit">{{
          $t("feedback.confirm")
        }}</el-button>
        <el-button @click="cancelEdit">{{ $t("feedback.cancel") }}</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  listFeedback,
  updateInvoiceInfo,
  exportFeedback,
  printTxt,
  downloadExcel,
  confirmFeedback
} from '@/api/datamanage/feedback'
import { listAsn } from '@/api/datamanage/asn'
import { commonDownloadFile } from '@/api/datamanage/common'
import { formatNow } from '@/utils/index'

export default {
  name: 'Feedback',
  data() {
    return {
      // 遮罩层
      loading: true,
      printTxtLoading: false,
      excelDownloading: false,
      confirmLoading: false,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 收货反馈表格数据
      feedbackList: [],
      // 弹出层标题
      // 是否显示弹出层
      open: false,
      editingFeedId: null,
      editCache: {},
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        dnNo: null,
        compCode: null,
        plantCode: null,
        plantName: null,
        suppCode: null,
        suppName: null,
        orderByColumn: 'createTime',
        isAsc: 'desc'
      }
    }
  },

  computed: {
    // 判断是否为固定仓库的运输商
    isCarrierWithDepot() {
      console.log(this.$store.state.user)
      const roles = this.$store.state.user.roles
      const remark = this.$store.state.user.remark
      return roles && roles.some(role => role === 'carrier') && remark
    }
  },
  created() {
    // if (this.$route.path.toLowerCase().endsWith('recv/feedback')) {
    //   this.queryParams.direction = 'I';
    // } else {
    //   this.queryParams.direction = 'O';
    // }
    this.getList()
  },
  methods: {
    /** 查询收货反馈列表 */
    getList() {
      this.loading = true
      listFeedback(this.queryParams).then((response) => {
        this.feedbackList = response.rows
        this.total = response.total
        this.loading = false
      })
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm('queryForm')
      this.handleQuery()
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.feedId)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 查看按钮操作 */
    async handleView(row) {
      this.reset()
      const asnCode = row.dnNo
      try {
        const response = await this.getASNId(asnCode)
        if (response?.rows?.length > 0) {
          const asnId = response.rows[0].asnId
          this.$router.push({ name: 'AsnDetail', params: { asnId }})
        } else {
          this.msgError(this.$t('feedback.asnCodeNotFound'))
        }
      } catch (error) {
        this.msgError(this.$t('feedback.asnCodeNotFound'))
      }
    },
    getASNId(asnCode) {
      const asnQueryParams = {
        pageNum: 1,
        pageSize: 1,
        asnCode,
        docno: null,
        compCode: null,
        suppCode: null,
        suppName: null,
        docdate: null,
        dnno: null,
        planDeliveryDate: null,
        deliveryDate: null,
        orderByColumn: 'createTime',
        isAsc: 'desc'
      }

      return listAsn(asnQueryParams)
    },
    /** 导出按钮操作 */
    handleExport() {
      const queryParams = this.queryParams
      this.$confirm(
        this.$t('feedback.confirmExport'),
        this.$t('feedback.warning'),
        {
          confirmButtonText: this.$t('feedback.confirm'),
          cancelButtonText: this.$t('feedback.cancel'),
          type: 'warning'
        }
      )
        .then(() => {
          return exportFeedback(queryParams)
        })
        .then((response) => {
          this.download(response.msg)
        })
        .catch(() => {})
    },
    handlePrintTxt() {
      if (this.ids.length === 0) {
        this.$alert(this.$t('feedback.alert.selectOrder'))
        return
      }
      this.printTxtLoading = true

      const loading = this.$loading({
        lock: false,
        fullscreen: true,
        text: this.$t('system.common.loading'),
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      })

      // 设置超时15秒后自动关闭loading
      const timeout = setTimeout(() => {
        loading.close()
      }, 15000)

      console.log(this.ids)
      printTxt(this.ids).then(response => {
        // 通过 window.open 打开 PDF
        window.open(process.env.VUE_APP_BASE_API + '/common/download?fileName=' + encodeURI(response.msg) + '&delete=true')
        loading.close()
        this.printTxtLoading = false
      }).catch(() => {
        loading.close()
        this.printTxtLoading = false // 如果请求失败，重置 loading 状态
      })
    },
    handleExcelDownload() {
      if (this.ids.length === 0) {
        this.$alert(this.$t('feedback.alert.selectAtLeastOne'))
        return
      }
      this.excelDownloading = true
      downloadExcel(this.ids)
        .then((res) => {
          commonDownloadFile(res.msg, `${this.$t('route.receiptFeedback')}${formatNow()}.xlsx`)
            .then(() => {
              this.excelDownloading = false
            })
        })
        .catch(() => {
          this.excelDownloading = false
        })
    },
    editRow(feedId) {
      const editingRow = this.feedbackList.find(d => d.feedId === feedId)
      if (editingRow) {
        this.editingFeedId = feedId
        this.editCache = { ...editingRow, invoiceTax: editingRow.invoiceTax === null ? undefined : editingRow.invoiceTax }
        this.open = true
      }
    },
    saveEdit() {
      const editingRow = this.feedbackList.find(d => d.feedId === this.editingFeedId)
      if (editingRow) {
        updateInvoiceInfo(this.editCache)
          .then(() => {
            this.open = false
            this.editingFeedId = null
            this.editCache = {}
            this.msgSuccess(this.$t('feedback.updateSuccess'))
            this.getList()
          }).catch(() => {})
      }
    },
    cancelEdit() {
      this.open = false
      this.editingFeedId = null
      this.editCache = {}
    },
    checkInvoiceConfirmInfo() {
      if (this.ids.length <= 0) {
        return false
      }
      const invoices = this.$refs.invoiceTableRef.selection
      var validate = true
      const reg = /^\d{1,18}(?:\.\d{1,2})?$/
      for (let i = 0; i < invoices.length; i++) {
        if (!reg.test(invoices[i].invoiceTax)) {
          validate = false
          break
        } else if (invoices[i].invoiceNo == null) {
          validate = false
          break
        } else if (invoices[i].invoiceDate == null) {
          validate = false
          break
        }
      }
      return validate
    },
    feedbackConfirm() {
      if (this.ids.length === 0) {
        this.$alert(this.$t('feedback.alert.selectAtLeastOne'))
        return
      }
      if (!this.checkInvoiceConfirmInfo()) {
        this.$alert(this.$t('feedback.alert.inValidInvoiceInfo'))
        return
      }
      this.confirmLoading = true
      confirmFeedback(this.ids)
        .then(async(res) => {
          if (this.ids.length === res?.data?.successList?.length) {
            this.msgSuccess(this.$t('order.success.confirm'))
          } else {
            const errorMessage = [...new Set(res?.data?.errorList?.map(el => el.message) ?? [])].join(',')
            this.msgError(errorMessage)
          }
          this.confirmLoading = false
          if (res?.data?.successList?.length > 0) {
            this.getList()
          }
        }).catch(() => {
          this.confirmLoading = false
        })
    }
  }
}
</script>

<style scoped>
.el-input-number,.el-date-editor {
  width: 100%
}

::v-deep .el-input-number input.el-input__inner {
  text-align: left;
}
</style>
